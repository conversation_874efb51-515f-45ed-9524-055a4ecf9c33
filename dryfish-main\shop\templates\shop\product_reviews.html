{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reviews - {{ product_display_name }}</title>
    <link rel="stylesheet" href="{% static 'shop/style.css' %}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .reviews-container {
            max-width: 1000px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .reviews-header {
            text-align: center;
            margin-bottom: 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
        }
        .rating-summary {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            text-align: center;
        }
        .star-display {
            font-size: 2rem;
            color: #ffc107;
            margin: 1rem 0;
        }
        .review-card {
            background: white;
            padding: 1.5rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }
        .review-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        .reviewer-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .review-rating {
            color: #ffc107;
            font-size: 1.2rem;
        }
        .review-date {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .review-text {
            color: #495057;
            line-height: 1.6;
        }
        .no-reviews {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        .action-buttons {
            text-align: center;
            margin: 2rem 0;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="/" class="logo">
                <i class="fas fa-fish"></i> JP Dry Fish
            </a>
            <ul class="nav-links">
                <li><a href="/">Home</a></li>
                <li><a href="/products/">Products</a></li>
                <li><a href="/order/">Order</a></li>
                {% if user.is_authenticated %}
                    <li><a href="/orders/">View Orders</a></li>
                    <li><a href="/accounts/logout/">Logout</a></li>
                {% else %}
                    <li><a href="/accounts/login/">Login</a></li>
                {% endif %}
            </ul>
        </div>
    </nav>

    <div class="reviews-container">
        <!-- Header -->
        <div class="reviews-header">
            <h1><i class="fas fa-star"></i> Customer Reviews</h1>
            <p>{{ product_display_name }}</p>
        </div>

        <!-- Messages -->
        {% if messages %}
            <div class="messages">
                {% for message in messages %}
                    <div class="message {% if message.tags == 'error' %}error{% else %}success{% endif %}">
                        <i class="fas {% if message.tags == 'error' %}fa-exclamation-circle{% else %}fa-check-circle{% endif %}"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <!-- Rating Summary -->
        <div class="rating-summary">
            <h3>Overall Rating</h3>
            <div class="star-display">{{ star_display }}</div>
            {% if average_rating %}
                <p>{{ average_rating|floatformat:1 }} out of 5 stars ({{ review_count }} review{{ review_count|pluralize }})</p>
            {% else %}
                <p>No reviews yet</p>
            {% endif %}
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            {% if user.is_authenticated %}
                {% if can_review and not user_has_reviewed %}
                    <a href="{% url 'submit_review' product_name %}" class="btn btn-success">
                        <i class="fas fa-star"></i> Write a Review
                    </a>
                {% elif user_has_reviewed %}
                    <a href="{% url 'submit_review' product_name %}" class="btn btn-outline">
                        <i class="fas fa-edit"></i> Edit Your Review
                    </a>
                {% else %}
                    <p style="color: #6c757d;">
                        <i class="fas fa-info-circle"></i> 
                        You need to purchase this product before you can review it.
                    </p>
                {% endif %}
            {% else %}
                <a href="{% url 'account_login' %}" class="btn btn-outline">
                    <i class="fas fa-sign-in-alt"></i> Login to Write a Review
                </a>
            {% endif %}
            
            <a href="{% url 'products' %}" class="btn btn-outline" style="margin-left: 1rem;">
                <i class="fas fa-arrow-left"></i> Back to Products
            </a>
        </div>

        <!-- Reviews List -->
        {% if reviews %}
            <div class="reviews-list">
                {% for review in reviews %}
                    <div class="review-card">
                        <div class="review-header">
                            <div class="reviewer-info">
                                <i class="fas fa-user-circle" style="font-size: 1.5rem; color: #6c757d;"></i>
                                <strong>{{ review.user.first_name|default:review.user.username }}</strong>
                            </div>
                            <div>
                                <div class="review-rating">{{ review.get_star_display }}</div>
                                <div class="review-date">{{ review.created_at|date:"M d, Y" }}</div>
                            </div>
                        </div>
                        {% if review.review_text %}
                            <div class="review-text">{{ review.review_text }}</div>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="no-reviews">
                <i class="fas fa-star" style="font-size: 3rem; color: #dee2e6; margin-bottom: 1rem;"></i>
                <h3>No Reviews Yet</h3>
                <p>Be the first to review {{ product_display_name }}!</p>
            </div>
        {% endif %}
    </div>

    <!-- WhatsApp Float -->
    <a href="https://wa.me/916369477095" class="whatsapp-float" target="_blank" title="Chat on WhatsApp">
        <i class="fab fa-whatsapp"></i>
    </a>

    <!-- Back to Top -->
    <a href="#" class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </a>

    <script>
        // Back to top functionality
        window.addEventListener('scroll', function() {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.classList.add('show');
            } else {
                backToTop.classList.remove('show');
            }
        });

        document.getElementById('backToTop').addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    </script>
</body>
</html>
