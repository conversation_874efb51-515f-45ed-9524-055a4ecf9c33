{% load socialaccount %}
{% load account %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - Dry Fish Shop</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .signup-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }
        h2 {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
            font-size: 28px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-weight: 500;
        }
        input[type="text"], input[type="email"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        input[type="text"]:focus, input[type="email"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #4285f4;
        }
        .btn {
            width: 100%;
            padding: 12px;
            background: #4285f4;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
            margin-bottom: 15px;
        }
        .btn:hover {
            background: #3367d6;
        }
        .google-btn {
            display: block;
            width: 100%;
            padding: 12px;
            background: #db4437;
            color: white;
            text-decoration: none;
            text-align: center;
            border-radius: 5px;
            font-size: 16px;
            transition: background 0.3s;
            margin-bottom: 20px;
        }
        .google-btn:hover {
            background: #c23321;
        }
        .divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
        }
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #ddd;
        }
        .divider span {
            background: white;
            padding: 0 15px;
            color: #666;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        .back-link a {
            color: #007bff;
            text-decoration: none;
        }
        .login-link {
            text-align: center;
            margin-top: 15px;
            color: #666;
        }
        .login-link a {
            color: #4285f4;
            text-decoration: none;
        }
        .signup-info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #4285f4;
        }
        .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="signup-container">
        <h2>Join Dry Fish Shop</h2>

        <div class="signup-info">
            <p><strong>Create Your Account</strong></p>
            <p>Sign up to place orders and track your purchases!</p>
        </div>

        {% if messages %}
            {% for message in messages %}
                <div class="error">{{ message }}</div>
            {% endfor %}
        {% endif %}

        <!-- Google OAuth Signup Button -->
        <a href="{% provider_login_url 'google' %}" class="google-btn">
            Sign up with Google
        </a>

        <div class="divider">
            <span>or</span>
        </div>

        <!-- Manual Registration Form -->
        <form method="post">
            {% csrf_token %}

            {% if form.errors %}
                <div class="error">
                    <p>Please correct the errors below:</p>
                    {{ form.errors }}
                </div>
            {% endif %}

            <div class="form-group">
                <label for="{{ form.email.id_for_label }}">Email Address:</label>
                {{ form.email }}
                {% if form.email.help_text %}
                    <div class="help-text">{{ form.email.help_text }}</div>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="{{ form.password1.id_for_label }}">Password:</label>
                {{ form.password1 }}
                {% if form.password1.help_text %}
                    <div class="help-text">{{ form.password1.help_text }}</div>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="{{ form.password2.id_for_label }}">Confirm Password:</label>
                {{ form.password2 }}
                {% if form.password2.help_text %}
                    <div class="help-text">{{ form.password2.help_text }}</div>
                {% endif %}
            </div>

            <button type="submit" class="btn">Create Account</button>

            <input type="hidden" name="next" value="{{ next }}" />
        </form>

        <div class="login-link">
            Already have an account? <a href="{% url 'account_login' %}">Sign in here</a>
        </div>

        <div class="back-link">
            <a href="/">← Back to Home</a>
        </div>
    </div>
</body>
</html>
