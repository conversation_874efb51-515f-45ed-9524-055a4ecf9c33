# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2024-11-08 10:00+0000\n"
"Last-Translator: SATOH Fumiyasu <<EMAIL>>\n"
"Language-Team: Japanese <https://hosted.weblate.org/projects/allauth/django-"
"allauth/ja/>\n"
"Language: ja\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 5.8.2\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "このアカウントは現在無効です。"

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "メインのメールアドレスを削除することはできません。"

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "このメールアドレスはすでに登録されています。"

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "入力されたメールアドレスもしくはパスワードが正しくありません。"

#: account/adapter.py:66
#, fuzzy
#| msgid "The username and/or password you specified are not correct."
msgid "The phone number and/or password you specified are not correct."
msgstr "入力されたユーザー名もしくはパスワードが正しくありません。"

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "他のユーザーがこのメールアドレスを使用しています。"

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "現在のパスワードを入力してください。"

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "コードが正しくありません。"

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "不正なパスワードです。"

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "不正または期限切れのキーです。"

#: account/adapter.py:73
#, fuzzy
#| msgid "Invalid token."
msgid "Invalid login."
msgstr "不正なトークンです。"

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "パスワードリセットトークンが無効です。"

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "メールアドレスは %d 個までしか登録できません。"

#: account/adapter.py:76
#, fuzzy
#| msgid "A user is already registered with this email address."
msgid "A user is already registered with this phone number."
msgstr "他のユーザーがこのメールアドレスを使用しています。"

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr "ログイン失敗が連続しています。時間が経ってからやり直してください。"

#: account/adapter.py:80
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The email address is not assigned to any user account."
msgstr "このメールアドレスで登録されたユーザーアカウントがありません"

#: account/adapter.py:81
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The phone number is not assigned to any user account."
msgstr "このメールアドレスで登録されたユーザーアカウントがありません"

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "メインのメールアドレスは確認済みでなければいけません。"

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr "このユーザー名は使用できません。他のユーザー名を選んでください。"

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "入力されたユーザー名もしくはパスワードが正しくありません。"

#: account/adapter.py:92
msgid "Please select only one."
msgstr ""

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr ""

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr ""

#: account/adapter.py:778
msgid "Use your password"
msgstr "パスワードを使用する"

#: account/adapter.py:787
msgid "Use authenticator app or code"
msgstr "認証アプリまたは認証コードを利用する"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
msgid "Use a security key"
msgstr "セキュリティキーを使用"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "確認済みにするメールアドレスを選択"

#: account/apps.py:11
msgid "Accounts"
msgstr "アカウント"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr ""

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr ""

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "同じパスワードを入力してください。"

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "パスワード"

#: account/forms.py:100
msgid "Remember Me"
msgstr "ログインしたままにする"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "メールアドレス"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "メールアドレス"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "ユーザー名"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "ログイン"

#: account/forms.py:154
#, fuzzy
#| msgid "Username or email"
msgid "Username, email or phone"
msgstr "ユーザー名またはメールアドレス"

#: account/forms.py:156
msgid "Username or email"
msgstr "ユーザー名またはメールアドレス"

#: account/forms.py:158
#, fuzzy
#| msgid "Username or email"
msgid "Username or phone"
msgstr "ユーザー名またはメールアドレス"

#: account/forms.py:160
#, fuzzy
#| msgid "Email (optional)"
msgid "Email or phone"
msgstr "メールアドレス (オプション)"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "パスワードをお忘れですか?"

#: account/forms.py:334
msgid "Email (again)"
msgstr "メールアドレス (確認用)"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "メールアドレスの確認"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "メールアドレス (オプション)"

#: account/forms.py:361
#, fuzzy
#| msgid "Email (optional)"
msgid "Username (optional)"
msgstr "メールアドレス (オプション)"

#: account/forms.py:435
msgid "You must type the same email each time."
msgstr "同じパスワードを入力してください。"

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "パスワード (再入力)"

#: account/forms.py:645
msgid "Current Password"
msgstr "現在のパスワード"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "新しいパスワード"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "新しいパスワード (再入力)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "コード"

#: account/models.py:26
msgid "user"
msgstr "ユーザー"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "メールアドレス"

#: account/models.py:34
msgid "verified"
msgstr "確認済み"

#: account/models.py:35
msgid "primary"
msgstr "メイン"

#: account/models.py:41
msgid "email addresses"
msgstr "メールアドレス"

#: account/models.py:151
msgid "created"
msgstr "作成日時"

#: account/models.py:152
msgid "sent"
msgstr "送信日時"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "キー"

#: account/models.py:158
msgid "email confirmation"
msgstr "メールアドレスの確認"

#: account/models.py:159
msgid "email confirmations"
msgstr "メールアドレスの確認"

#: headless/apps.py:7
msgid "Headless"
msgstr "ヘッドレス"

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"多要素認証で保護されているアカウントには、メールアドレスを追加できません。"

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr "多要素認証は無効化できません。"

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr "リカバリーキーを生成するには多要素認証の有効化が必要です。"

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"メールアドレスの確認が完了するまで、多要素認証を有効にすることはできません。"

#: mfa/adapter.py:141
msgid "Master key"
msgstr "マスターキー"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr "バックアップキー"

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr "キー番号 {number}"

#: mfa/apps.py:9
msgid "MFA"
msgstr "多要素認証"

#: mfa/models.py:24
msgid "Recovery codes"
msgstr "リカバリーコード"

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr "TOTP 認証アプリ"

#: mfa/models.py:26
msgid "WebAuthn"
msgstr "WebAuthn"

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr "認証アプリコード"

#: mfa/webauthn/forms.py:59
msgid "Passwordless"
msgstr "パスワードレス"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""
"パスワードレス機能を有効化すると、このキーを使用してログイン可能になります。"
"その代わりに生態認証や PIN コードなどによる保護が必要になります。"

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"このメールアドレスを使用するアカウントが既にあります。そのアカウントにログイ"
"ンしてから %s アカウントを接続してください。"

#: socialaccount/adapter.py:39
msgid "Invalid token."
msgstr "不正なトークンです。"

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "アカウントにパスワードを設定する必要があります。"

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "確認済みのメールアドレスの登録が必要です。"

#: socialaccount/adapter.py:43
msgid "You cannot disconnect your last remaining third-party account."
msgstr "唯一残っている外部アカウントとの接続を解除することはできません。"

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The third-party account is already connected to a different account."
msgstr "この外部アカウントは他のアカウントに接続されています。"

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "外部アカウント"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "プロバイダー"

#: socialaccount/models.py:52
msgid "provider ID"
msgstr "プロバイダー ID"

#: socialaccount/models.py:56
msgid "name"
msgstr "ユーザー名"

#: socialaccount/models.py:58
msgid "client id"
msgstr "クライアント ID"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "App ID もしくはコンシューマキー"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "シークレットキー"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr ""
"API シークレット、クライアントシークレット、またはコンシューマーシークレット"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "キー"

#: socialaccount/models.py:81
msgid "social application"
msgstr "ソーシャルアプリケーション"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "ソーシャルアプリケーション"

#: socialaccount/models.py:117
msgid "uid"
msgstr "ユーザー名"

#: socialaccount/models.py:119
msgid "last login"
msgstr "最終ログイン"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "アカウント作成日"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "エクストラデータ"

#: socialaccount/models.py:125
msgid "social account"
msgstr "ソーシャルアカウント"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "ソーシャルアカウント"

#: socialaccount/models.py:160
msgid "token"
msgstr "トークン"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "「oauth_token」(OAuth1) もしくは Access Token (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "トークンシークレット"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "「oauth_token_secret」(OAuth1) もしくは Refresh Token (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "失効期限"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "ソーシャルアプリケーショントークン"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "ソーシャルアプリケーショントークン"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "無効なプロファイルデータ"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
msgid "Login"
msgstr "ログイン"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr "キャンセル"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr ""
"リクエストトークン取得中に「%s」から不正な応答がありました。レスポンス: %s"

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr ""
"不正なレスポンスが返されたため、「%s」からアクセストークンを取得できませんで"
"した。"

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "「%s」のリクエストトークンを保存できませんでした。"

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "「%s」のアクセストークンを保存できませんでした。"

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "「%s」の情報にアクセスできませんでした。"

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr ""
"不正なレスポンスが返されたため、「%s」からリクエストトークンを取得できません"
"でした。"

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "無効なアカウント"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "このアカウントは無効です。"

#: templates/account/base_confirm_code.html:27
#, fuzzy, python-format
#| msgid ""
#| "We’ve sent a code to %(email_link)s. The code expires shortly, so please "
#| "enter it soon."
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr ""
"コードを %(email_link)s に送信しました。コードの有効期限はまもなく切れますの"
"で、お早めにご入力ください。"

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "確認する"

#: templates/account/base_confirm_code.html:43
#, fuzzy
#| msgid "Request Code"
msgid "Request new code"
msgstr "コードを要求"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "アクセス確認"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr "アカウントを保護するために再認証してください。"

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr "そのほかの方法"

#: templates/account/confirm_email_verification_code.html:5
msgid "Email Verification"
msgstr "メールアドレスの確認"

#: templates/account/confirm_email_verification_code.html:8
msgid "Enter Email Verification Code"
msgstr "メールの確認コードを入力:"

#: templates/account/confirm_email_verification_code.html:16
#, fuzzy
#| msgid "email address"
msgid "Use a different email address"
msgstr "メールアドレス"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "ログイン"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr "ログインコードを入力"

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "パスワード再設定"

#: templates/account/confirm_password_reset_code.html:8
#, fuzzy
#| msgid "Password Reset"
msgid "Enter Password Reset Code"
msgstr "パスワード再設定"

#: templates/account/confirm_phone_verification_code.html:5
#, fuzzy
#| msgid "Re-send Verification"
msgid "Phone Verification"
msgstr "確認メールを再送する"

#: templates/account/confirm_phone_verification_code.html:8
#, fuzzy
#| msgid "Enter Email Verification Code"
msgid "Enter Phone Verification Code"
msgstr "メールの確認コードを入力:"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "メールアドレス"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr "以下のメールアドレスがアカウントに登録されています:"

#: templates/account/email.html:25
msgid "Verified"
msgstr "確認済み"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "未確認"

#: templates/account/email.html:34
msgid "Primary"
msgstr "メイン"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "メインにする"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "確認メールを再送する"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "削除"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "メールアドレスの登録"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "メールアドレスの登録"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "選択されたメールアドレスを削除してもよろしいですか?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"このメールを受信しているのは、あなたまたは誰かが次のメールアドレスを使用して"
"アカウントの登録を試みたためです:\n"
"\n"
"%(email)s\n"
"\n"
"しかし、このメールアドレスを使用したアカウントがすでに存在しています。もしア"
"カウントの登録を忘れていた場合は、パスワードを忘れたときの手続きを利用してア"
"カウントを回復してください:\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "アカウントが既に存在"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "こんにちは、%(site_name)s です!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"%(site_name)s をご利用いただきありがとうございます!\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr ""
"あなたのアカウントに以下の変更が加えられたため、このメールが届いています:"

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""
"この変更が認識できない場合は、直ちに適切なセキュリティ対策を講じてください。"
"あなたのアカウントに対するこの変更の起源は以下の通りです：\n"
"\n"
"- IPアドレス: %(ip)s\n"
"- ブラウザ: %(user_agent)s\n"
"- 日付: %(timestamp)s"

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr ""
"あなたのメールアドレスを %(from_email)s から %(to_email)s に変更しました。"

#: templates/account/email/email_changed_subject.txt:3
msgid "Email Changed"
msgstr "メールアドレスを変更しました"

#: templates/account/email/email_confirm_message.txt:4
msgid "Your email has been confirmed."
msgstr "あなたのメールアドレスを確認しました。"

#: templates/account/email/email_confirm_subject.txt:3
msgid "Email Confirmation"
msgstr "メールアドレスの確認"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"%(user_display)s さんが %(site_domain)s にあなたのメールアドレスを登録しよう"
"としています。"

#: templates/account/email/email_confirmation_message.txt:7
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"あなたのメール確認コードを下記に記載します。開いているブラウザのウィンドウに"
"入力してください。"

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr "間違いなければ、%(activate_url)s にアクセスしてください。"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "メールアドレスを確認してください"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr ""
"メールアドレス %(deleted_email)s をあなたのアカウントから削除しました。"

#: templates/account/email/email_deleted_subject.txt:3
msgid "Email Removed"
msgstr "メールアドレスの削除"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr ""
"以下にあなたのログインコードが記載されています。開いているブラウザのウィンド"
"ウに入力してください。"

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr ""
"この操作があなたによるものではない場合、このメールは無視しても差し支えありま"
"せん。"

#: templates/account/email/login_code_subject.txt:3
msgid "Sign-In Code"
msgstr "ログインコード"

#: templates/account/email/password_changed_message.txt:4
msgid "Your password has been changed."
msgstr "パスワードを変更しました。"

#: templates/account/email/password_changed_subject.txt:3
msgid "Password Changed"
msgstr "パスワードを変更しました"

#: templates/account/email/password_reset_code_message.txt:5
#, fuzzy
#| msgid ""
#| "Your sign-in code is listed below. Please enter it in your open browser "
#| "window."
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"以下にあなたのログインコードが記載されています。開いているブラウザのウィンド"
"ウに入力してください。"

#: templates/account/email/password_reset_code_subject.txt:3
#, fuzzy
#| msgid "Password Reset"
msgid "Password Reset Code"
msgstr "パスワード再設定"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"このメールは、あなた (もしくは別の誰か) がパスワードの再設定を行おうとしたた"
"めに送られました。\n"
"パスワードの再設定を要求したのがあなたではない場合、このメールは無視してくだ"
"さい。\n"
"パスワードを再設定するためには、以下のリンクをクリックしてください。"

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "あなたのアカウントのユーザー名は %(username)s です。"

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "パスワード再設定メール"

#: templates/account/email/password_reset_message.txt:4
msgid "Your password has been reset."
msgstr "パスワードをリセットしました。"

#: templates/account/email/password_set_message.txt:4
msgid "Your password has been set."
msgstr "パスワードを設定しました。"

#: templates/account/email/password_set_subject.txt:3
msgid "Password Set"
msgstr "パスワード設定"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""
"このメールは、あなたまたは他の誰かが %(email)s のメールアドレスを持つアカウン"
"トにアクセスしようとしたために送信されています。ただし、私たちのデータベース"
"にはそのようなアカウントの記録はありません。"

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr "もしあなたで間違いないなら、以下のリンクからアカウントを登録できます。"

#: templates/account/email/unknown_account_subject.txt:3
msgid "Unknown Account"
msgstr "不明なアカウント"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "メールアドレス"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "現在のメールアドレス"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "変更中"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr "あなたのメールアドレスはまだ確認されていません。"

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "変更をキャンセル"

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
msgid "Change to"
msgstr "変更する"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
msgid "Change Email"
msgstr "メールアドレス変更"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "メールアドレスの確認"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"メールアドレス <a href=\"mailto:%(email)s\">%(email)s</a> がユーザー "
"%(user_display)s さんのものであることを確認してください。"

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "%(email)s はすでにほかのアカウントで確認済みです。"

#: templates/account/email_confirm.html:36
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"メールアドレス確認用のリンクが不正か、期限が切れています。<a "
"href=\"%(email_url)s\">確認用のメールを再送</a>してください。"

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr ""
"アカウントをまだお持ちでなければ、 %(link)sこちらからユーザー登"
"録%(end_link)s してください。"

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr "パスキーでログイン"

#: templates/account/login.html:47 templates/account/request_login_code.html:9
#, fuzzy
#| msgid "Mail me a sign-in code"
msgid "Send me a sign-in code"
msgstr "ログインコードをメールする"

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "ログアウト"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "ログアウトしますか?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "メインのメールアドレス (%(email)s) を削除することはできません。"

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "確認メールを %(email)s へ送信しました。"

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "%(email)s を確認しました。"

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "メールアドレス %(email)s を削除しました。"

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "ユーザー %(name)s としてログインしました。"

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "ログアウトしました。"

#: templates/account/messages/login_code_sent.txt:2
#, python-format
msgid "A sign-in code has been sent to %(recipient)s."
msgstr "ログインコードを %(recipient)s へ送信しました。"

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "パスワードを変更しました。"

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "パスワードを設定しました。"

#: templates/account/messages/phone_verification_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been sent to %(recipient)s."
msgid "A verification code has been sent to %(phone)s."
msgstr "ログインコードを %(recipient)s へ送信しました。"

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr ""

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "メインメールアドレスを設定しました。"

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "パスワード変更"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "パスワードをお忘れですか?"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"パスワードをお忘れですか? パスワードをリセットするために、メールアドレスを入"
"力してください。"

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "パスワードをリセット"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr "パスワードの再設定に問題がある場合はご連絡ください。"

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"確認のためのメールを送信しました。確認メールが届かないときは迷惑メールフォル"
"ダをご確認ください。しばらくしてもメールが届かない場合はご連絡ください。"

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "不正なトークン"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"パスワード再設定用のリンクが不正です。すでに使用された可能性があります。もう"
"一度 <a href=\"%(passwd_reset_url)s\">パスワードの再設定</a>をお試しくださ"
"い。"

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "パスワードを変更しました。"

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "パスワード設定"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
#, fuzzy
#| msgid "Change to"
msgid "Change Phone"
msgstr "変更する"

#: templates/account/phone_change.html:18
#, fuzzy
#| msgid "Current"
msgid "Current phone"
msgstr "現在の"

#: templates/account/phone_change.html:22
#, fuzzy
#| msgid "Your email address is still pending verification."
msgid "Your phone number is still pending verification."
msgstr "あなたのメールアドレスはまだ確認されていません。"

#: templates/account/reauthenticate.html:6
msgid "Enter your password:"
msgstr "パスワードを入力してください。"

#: templates/account/request_login_code.html:12
#, fuzzy
#| msgid ""
#| "You will receive an email containing a special code for a password-free "
#| "sign-in."
msgid "You will receive a special code for a password-free sign-in."
msgstr "パスワード不要のログイン用特別コードが記載されたメールをお送りします。"

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr "コードを要求"

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr "そのほかのログイン方法"

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "ユーザー登録"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "ユーザー登録"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr ""
"すでにアカウントをお持ちであれば、こちらから %(link)sログイン%(end_link)s し"
"てください。"

#: templates/account/signup.html:39
msgid "Sign up using a passkey"
msgstr "パスキーでユーザー登録"

#: templates/account/signup_by_passkey.html:8
msgid "Passkey Sign Up"
msgstr "パスキーでユーザー登録"

#: templates/account/signup_by_passkey.html:36
msgid "Other options"
msgstr "ほかのオプション"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "ユーザー登録停止中"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "申し訳ありません、現在ユーザー登録を停止しています。"

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "注意"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "%(user_display)s さんとしてすでにログイン中です。"

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "注意:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"メールアドレスが設定されていません。通知を受け取ったり、パスワードをリセット"
"したりするためにはメールアドレスを登録する必要があります。"

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "メールアドレスを確認してください"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"確認のメールを送信しました。メールに記載されたリンクをクリックして、ユーザー"
"登録を完了させてください。確認メールが届かないときは迷惑メールフォルダをご確"
"認ください。しばらくしても確認のメールが届かない場合はお問い合わせください。"

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"このページにアクセスするためには、本人確認が必要です。\n"
"そのために、登録されているメールアドレスがご自身のものであることを確認してい"
"ただきます。 "

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"確認のためのメールを送信しました。メールに記載されたリンクをクリックしてくだ"
"さい。\n"
"確認メールが届かないときは迷惑メールフォルダをご確認ください。\n"
"しばらくしてもメールが届かない場合はお問い合わせください。"

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>注意:</strong> <a href=\"%(email_url)s\">メールアドレスの変更</a>をし"
"ていただくことも可能です。"

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "メッセージ:"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "メニュー:"

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "アカウント接続"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "多要素認証"

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr "セッション"

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""
"あなたのアカウントは多要素認証で保護されています。認証コードを入力してくださ"
"い:"

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr "多要素認証用のリカバリーコードを新たに生成しました。"

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr "新しいリカバリーコードを生成しました"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "認証アプリを有効化しました。"

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr "認証アプリを有効化しました"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "認証アプリを無効化しました。"

#: templates/mfa/email/totp_deactivated_subject.txt:3
msgid "Authenticator App Deactivated"
msgstr "認証アプリを無効化しました"

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr "新しいセキュリティキーを登録しました。"

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr "セキュリティキーを追加しました"

#: templates/mfa/email/webauthn_removed_message.txt:4
msgid "A security key has been removed."
msgstr "セキュリティキーを削除しました。"

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr "セキュリティキーを削除しました"

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "認証アプリ"

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr "認証アプリを使用した認証が有効です。"

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr "認証アプリは有効ではありません。"

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "無効化する"

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "有効化する"

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr "セキュリティキー"

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] "%(count)s 個のセキュリティキーを登録しました。"

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr "登録されているセキュリティキーはありません。"

#: templates/mfa/index.html:62
msgid "Manage"
msgstr "管理"

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr "登録"

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "リカバリーコード"

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
"利用可能なリカバリーコードは %(total_count)s 個中 %(unused_count)s 個です。"

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr "リカバリーコードが設定されていません。"

#: templates/mfa/index.html:96
msgid "View"
msgstr "表示"

#: templates/mfa/index.html:102
msgid "Download"
msgstr "ダウンロード"

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "生成する"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "新しいリカバリーコードを生成しました。"

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr "セキュリティーキーを登録しました。"

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr "セキュリティキーを削除しました。"

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr "認証アプリのコードを入力:"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr "新しいリカバリーコードセットを生成しようとしています。"

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "この操作により、現在のコードが無効になります。"

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "本当によろしいですか?"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "未使用のコード"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "コードをダウンロードする"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "新しいコードを生成する"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "認証アプリを有効化する"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"アカウントを多要素認証で保護するために、認証アプリで以下の QR コードをスキャ"
"ンしてください。次に、アプリで生成された確認コードを以下に入力してください。"

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "認証アプリシークレット"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""
"このシークレットを保存し、後で認証アプリを再インストールする際に使用できま"
"す。"

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "認証アプリを無効化する"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr "認証アプリによる認証を無効化しようとしています。本当によろしいですか?"

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr ""

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr ""

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr ""

#: templates/mfa/webauthn/add_form.html:7
msgid "Add Security Key"
msgstr "セキュリティキーを追加"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
msgid "Remove Security Key"
msgstr "セキュリティキーを解除"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
msgid "Are you sure you want to remove this security key?"
msgstr "セキュリティキーを解除しますか?"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr "使用方法"

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr "パスキー"

#: templates/mfa/webauthn/authenticator_list.html:37
msgid "Security key"
msgstr "セキュリティキー"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr "このキーはパスキーかどうか示されていません。"

#: templates/mfa/webauthn/authenticator_list.html:41
msgid "Unspecified"
msgstr "未指定"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr "登録日: %(created_at)s"

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr "最終使用日: %(last_used)s"

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr "編集"

#: templates/mfa/webauthn/edit_form.html:7
msgid "Edit Security Key"
msgstr "セキュリティキーを編集"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr "保存"

#: templates/mfa/webauthn/signup_form.html:7
msgid "Create Passkey"
msgstr "パスキーを作成"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""
"あなたのアカウント用のパスキーを作成します。あとでキーの追加もできるため、"
"キーを区別しやすい名前を付けてください。"

#: templates/mfa/webauthn/signup_form.html:21
msgid "Create"
msgstr "作成"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr "この機能を利用するには JavaScript が必要です。"

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Third-Party Login Failure"
msgstr "外部アカウントによるログインに失敗しました"

#: templates/socialaccount/authentication_error.html:12
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr "外部アカウントでのログイン時ににエラーが発生しました。"

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr "以下の外部アカウントを使ってログインできます:"

#: templates/socialaccount/connections.html:46
msgid "You currently have no third-party accounts connected to this account."
msgstr "あなたのカウントに結びつけられた外部アカウントはありません。"

#: templates/socialaccount/connections.html:50
msgid "Add a Third-Party Account"
msgstr "外部アカウントを追加する"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr "%(provider)s の外部アカウントをあなたのアカウントに接続しました。"

#: templates/socialaccount/email/account_connected_subject.txt:3
msgid "Third-Party Account Connected"
msgstr "外部アカウントを接続しました"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr "%(provider)s の外部アカウントへの接続を解除しました。"

#: templates/socialaccount/email/account_disconnected_subject.txt:3
msgid "Third-Party Account Disconnected"
msgstr "外部アカウントとの接続を解除しました"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "%(provider)s と接続する"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr "%(provider)s の新しい外部アカウントを接続しようとしています。"

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "%(provider)s でログイン"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr "%(provider)s の外部アカウントを使用してログインしようとしています。"

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr "続ける"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "ログインをキャンセルしました"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"既存の外部アカウントを使ったログインをキャンセルしました。\n"
"やり直す場合は<a href=\"%(login_url)s\">ログインページ</a>にお進みください。"

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The third-party account has been connected."
msgstr "外部アカウントを接続しました。"

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The third-party account has been disconnected."
msgstr "外部アカウントとの接続を解除しました。"

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"%(provider_name)s アカウントを使って %(site_name)s にログインしようとしていま"
"す。\n"
"ユーザー登録のために、以下のフォームに記入してください。"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr "外部アカウントを使用する"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr "他のすべてのセッションからログアウトしました。"

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr "開始日時"

#: templates/usersessions/usersession_list.html:24
msgid "IP Address"
msgstr "IP アドレス"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr "ブラウザー"

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr "閲覧日時"

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "現在の"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr "他のセッションをログアウト"

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr "ユーザーセッション"

#: usersessions/models.py:92
msgid "session key"
msgstr "セッションキー"

#, fuzzy
#~| msgid "Account Connections"
#~ msgid "Account Connection"
#~ msgstr "アカウント接続"

#~ msgid "Use security key or device"
#~ msgstr "セキュリティキー/デバイスを使用"

#~ msgid "Add Security Key or Device"
#~ msgstr "セキュリティキー/デバイスの登録"

#~ msgid "Add key or device"
#~ msgstr "キー/デバイスを登録"

#~ msgid "Security Keys and Devices"
#~ msgstr "セキュリティキー/デバイス"

#~ msgid "You have not added any security keys/devices."
#~ msgstr "登録されたセキュリティキー/デバイスはありません。"

#~ msgid "Edit Security Key or Device"
#~ msgstr "セキュリティキー/デバイスの編集"

#, python-brace-format
#~ msgid "Password must be a minimum of {0} characters."
#~ msgstr "パスワードは {0} 文字以上の長さが必要です。"

#~| msgid ""
#~| "You're receiving this e-mail because you or someone else has requested a "
#~| "password for your user account.\n"
#~| "It can be safely ignored if you did not request a password reset. Click "
#~| "the link below to reset your password."
#~ msgid ""
#~ "You are receiving this email because you or someone else has requested a\n"
#~ "password for your user account. However, we do not have any record of a "
#~ "user\n"
#~ "with email %(email)s in our database.\n"
#~ "\n"
#~ "This mail can be safely ignored if you did not request a password reset.\n"
#~ "\n"
#~ "If it was you, you can sign up for an account using the link below."
#~ msgstr ""
#~ "このメールは、あなた（もしくは別の誰か）がパスワードの再設定を行おうとした"
#~ "ために送られました。\n"
#~ "パスワードの再設定を要求したのがあなたではない場合、このメールは無視してく"
#~ "ださい。パスワードを再設定するためには、以下のリンクをクリックしてくださ"
#~ "い。"

#, fuzzy
#~| msgid "The following email addresses are associated with your account:"
#~ msgid "The following email address is associated with your account:"
#~ msgstr "以下のメールアドレスがアカウントに登録されています："

#, fuzzy
#~| msgid "Confirm Email Address"
#~ msgid "Change Email Address"
#~ msgstr "メールアドレスの確認"

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "お持ちの外部アカウントでログインするか、%(site_name)sに <a "
#~ "href=\"%(signup_url)s\">ユーザー登録</a> してログインしてください。"

#~ msgid "or"
#~ msgstr "または"

#~ msgid "change password"
#~ msgstr "パスワード変更"

#~ msgid "OpenID Sign In"
#~ msgstr "OpenID ログイン"

#~ msgid "This email address is already associated with another account."
#~ msgstr "このメールアドレスは別のアカウントで使用されています。"
