from django.contrib import admin
from django.urls import path
from django.contrib.auth import views as auth_views
from shop import views

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', views.home, name='home'),
    path('products/', views.products, name='products'),
    path('order/', views.order, name='order'),
    path('orders/', views.view_orders, name='view_orders'),
    path('payment/<int:order_id>/', views.payment, name='payment'),
    path('payment/process/<int:order_id>/', views.payment_process, name='payment_process'),
    path('payment/verify/', views.payment_verify, name='payment_verify'),
    path('payment/confirmation/<int:order_id>/', views.payment_confirmation, name='payment_confirmation'),
    path('payment/success/<int:order_id>/', views.payment_success, name='payment_success'),
    path('export/', views.export_orders, name='export_orders'),

    # Review URLs
    path('review/<str:product_name>/', views.submit_review, name='submit_review'),
    path('reviews/<str:product_name>/', views.product_reviews, name='product_reviews'),

    # Authentication URLs
    path('accounts/login/', views.login_view, name='account_login'),
    path('accounts/logout/', views.logout_view, name='account_logout'),
    path('accounts/signup/', views.signup_view, name='account_signup'),
]
