{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Dry Fish - JP Dry Fish</title>
    <link rel="stylesheet" href="{% static 'shop/style.css' %}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="/" class="logo">
                <i class="fas fa-fish"></i> JP Dry Fish
            </a>
            <ul class="nav-links">
                <li><a href="/">Home</a></li>
                <li><a href="/products/">Products</a></li>
                <li><a href="/order/" class="active">Order</a></li>
                {% if user.is_authenticated %}
                    <li><a href="/orders/">View Orders</a></li>
                    <li><a href="/accounts/logout/">Logout</a></li>
                {% else %}
                    <li><a href="/accounts/login/">Login</a></li>
                {% endif %}
            </ul>
        </div>
    </nav>

    <div class="container" style="padding-top: 2rem;">
        <!-- Header -->
        <div class="text-center mb-4">
            <h1 style="font-size: 2.5rem; color: #1f2937; margin-bottom: 0.5rem;">
                <i class="fas fa-shopping-cart"></i> Place Your Order
            </h1>
            <p style="color: #6b7280; font-size: 1.1rem;">Fill in your details to get fresh dry fish delivered</p>
        </div>

        <!-- User Welcome Message -->
        {% if user.is_authenticated %}
            <div style="background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%); padding: 1.5rem; border-radius: 12px; margin-bottom: 2rem; border-left: 4px solid #3b82f6;">
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-user-check" style="color: #3b82f6;"></i>
                    <strong style="color: #1e40af;">Welcome, {{ user.first_name|default:user.email }}!</strong>
                </div>
                <p style="margin: 0.5rem 0 0 0; color: #1e40af;">You're logged in with your Google account. Your order will be linked to your account for easy tracking.</p>
            </div>
        {% endif %}

        <!-- Messages -->
        {% if messages %}
            <div class="messages">
                {% for message in messages %}
                    <div class="message {% if message.tags == 'error' %}error{% else %}success{% endif %}">
                        <i class="fas {% if message.tags == 'error' %}fa-exclamation-circle{% else %}fa-check-circle{% endif %}"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <!-- Order Form -->
        <div class="form-container">
            <form method="post" class="fade-in-up">
                {% csrf_token %}

                <div class="form-group">
                    <label for="name">
                        <i class="fas fa-user"></i> Full Name
                    </label>
                    <input type="text" id="name" name="name" class="form-control"
                           placeholder="Enter your full name"
                           value="{{ form.name.value|default:user_data.name|default:'' }}" required>
                </div>

                <div class="form-group">
                    <label for="mobile">
                        <i class="fas fa-phone"></i> Mobile Number
                    </label>
                    <input type="tel" id="mobile" name="mobile" class="form-control"
                           placeholder="Enter your mobile number"
                           value="{{ form.mobile.value|default:'' }}" required>
                </div>

                <!-- Detailed Delivery Address Section -->
                <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 1.5rem; border-radius: 12px; margin: 1.5rem 0; border-left: 4px solid #28a745;">
                    <h4 style="color: #155724; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-map-marker-alt"></i> Delivery Address Details
                    </h4>
                    <p style="color: #155724; margin-bottom: 1rem; font-size: 0.9rem;">
                        Please provide your complete delivery address for accurate delivery.
                    </p>

                    <div class="form-group">
                        <label for="street_name">
                            <i class="fas fa-road"></i> Street Address
                        </label>
                        <input type="text" id="street_name" name="street_name" class="form-control"
                               placeholder="House number, street name"
                               value="{{ form.street_name.value|default:'' }}" required>
                    </div>

                    <div class="form-group">
                        <label for="place_name">
                            <i class="fas fa-map-pin"></i> Area/Locality
                        </label>
                        <input type="text" id="place_name" name="place_name" class="form-control"
                               placeholder="Area, locality, landmark"
                               value="{{ form.place_name.value|default:'' }}" required>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label for="city">
                                <i class="fas fa-city"></i> City
                            </label>
                            <input type="text" id="city" name="city" class="form-control"
                                   placeholder="City name"
                                   value="{{ form.city.value|default:'' }}" required>
                        </div>

                        <div class="form-group">
                            <label for="state">
                                <i class="fas fa-flag"></i> State
                            </label>
                            <input type="text" id="state" name="state" class="form-control"
                                   placeholder="State name"
                                   value="{{ form.state.value|default:'' }}" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="pin_code">
                            <i class="fas fa-mail-bulk"></i> PIN Code
                        </label>
                        <input type="text" id="pin_code" name="pin_code" class="form-control"
                               placeholder="6-digit PIN code" pattern="[0-9]{6}" maxlength="6"
                               value="{{ form.pin_code.value|default:'' }}" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="address">
                        <i class="fas fa-sticky-note"></i> Additional Address Info (Optional)
                    </label>
                    <textarea id="address" name="address" class="form-control"
                              placeholder="Any additional delivery instructions or address details" rows="3">{{ form.address.value|default:'' }}</textarea>
                    <small style="color: #6c757d; font-size: 0.85rem;">
                        <i class="fas fa-info-circle"></i> Optional: Add any special delivery instructions or additional address details
                    </small>
                </div>

                <div class="form-group">
                    <label for="product">
                        <i class="fas fa-fish"></i> Select Product
                    </label>
                    <select id="product" name="product" class="form-control" required>
                        <option value="">Choose a product...</option>
                        <option value="motha kendai" {% if form.product.value == 'motha kendai' or pre_selected_product == 'motha kendai' %}selected{% endif %}>Motha Kendai - Traditional favorite for curries</option>
                        <option value="netthili" {% if form.product.value == 'netthili' or pre_selected_product == 'netthili' %}selected{% endif %}>Netthili - Perfect for frying and side dishes</option>
                        <option value="vaalai" {% if form.product.value == 'vaalai' or pre_selected_product == 'vaalai' %}selected{% endif %}>Vaalai - Soft texture, ideal for gravies</option>
                        <option value="goa netthili" {% if form.product.value == 'goa netthili' or pre_selected_product == 'goa netthili' %}selected{% endif %}>Goa Netthili - Coastal flavors, larger size</option>
                        <option value="yeera" {% if form.product.value == 'yeera' or pre_selected_product == 'yeera' %}selected{% endif %}>Yeera - Intense umami for chutneys</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="quantity">
                        <i class="fas fa-weight-hanging"></i> Quantity (kg)
                    </label>
                    <input type="number" id="quantity" name="quantity" class="form-control"
                           placeholder="Enter quantity in kg" min="0.5" step="0.5"
                           value="{{ form.quantity.value|default:'' }}" required>
                </div>

                <!-- Price Display -->
                <div class="form-group" id="priceDisplay" style="display: none;">
                    <div style="background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); padding: 1.5rem; border-radius: 12px; border-left: 4px solid #0ea5e9;">
                        <h4 style="color: #0c4a6e; margin-bottom: 0.5rem;">
                            <i class="fas fa-calculator"></i> Price Calculation
                        </h4>
                        <div id="priceBreakdown" style="color: #0c4a6e;"></div>
                    </div>
                </div>

                <div class="text-center">
                    <button type="submit" class="btn btn-success" style="padding: 1rem 2rem; font-size: 1.1rem;">
                        <i class="fas fa-shopping-cart"></i> Proceed to Payment
                    </button>
                </div>
            </form>
        </div>

        <!-- Contact Support Section -->
        <div style="background: linear-gradient(135deg, #e8f5e8 0%, #f0f9ff 100%); padding: 2rem; border-radius: 15px; margin: 2rem 0; text-align: center; border-left: 4px solid #25d366;">
            <h3 style="color: #0c4a6e; margin-bottom: 1rem;">
                <i class="fab fa-whatsapp" style="color: #25d366;"></i> Need Help with Your Order?
            </h3>
            <p style="color: #374151; margin-bottom: 1.5rem;">
                Have questions about products, pricing, or delivery? Contact us directly on WhatsApp for instant support!
            </p>
            <a href="https://wa.me/916369477095?text=Hi! I need help with placing an order for dry fish from JP Dry Fish, Gudiyattam."
               class="btn"
               style="background: #25d366; color: white; padding: 1rem 2rem; border-radius: 25px; text-decoration: none; display: inline-flex; align-items: center; gap: 0.5rem; font-weight: 600; transition: all 0.3s ease;"
               target="_blank"
               onmouseover="this.style.background='#128c7e'; this.style.transform='scale(1.05)'"
               onmouseout="this.style.background='#25d366'; this.style.transform='scale(1)'">
                <i class="fab fa-whatsapp" style="font-size: 1.2em;"></i>
                Chat with JP Dry Fish
            </a>
            <div style="margin-top: 1rem; color: #6b7280; font-size: 0.9rem;">
                <i class="fas fa-phone"></i> +91 6369477095 |
                <i class="fas fa-map-marker-alt"></i> Gudiyattam, Tamil Nadu
            </div>
        </div>

        <!-- Back to Home -->
        <div class="text-center" style="margin: 2rem 0;">
            <a href="/" class="btn btn-outline">
                <i class="fas fa-arrow-left"></i> Back to Home
            </a>
        </div>
    </div>

    <!-- WhatsApp Float -->
    <a href="https://wa.me/916369477095" class="whatsapp-float" target="_blank" title="Chat on WhatsApp">
        <i class="fab fa-whatsapp"></i>
    </a>

    <!-- Back to Top -->
    <a href="#" class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </a>

    <script>
        // Back to top functionality
        window.addEventListener('scroll', function() {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.classList.add('show');
            } else {
                backToTop.classList.remove('show');
            }
        });

        document.getElementById('backToTop').addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });

        // Product prices (per kg) - Updated to match model
        const productPrices = {
            'motha kendai': 450,
            'netthili': 400,
            'vaalai': 300,
            'goa netthili': 600,
            'yeera': 500
        };

        // Price calculation
        function updatePrice() {
            const product = document.getElementById('product').value;
            const quantity = parseFloat(document.getElementById('quantity').value) || 0;
            const priceDisplay = document.getElementById('priceDisplay');
            const priceBreakdown = document.getElementById('priceBreakdown');

            if (product && quantity > 0) {
                const unitPrice = productPrices[product];
                const totalPrice = unitPrice * quantity;

                priceBreakdown.innerHTML = `
                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                        <span>${product.charAt(0).toUpperCase() + product.slice(1)} (${quantity} kg × ₹${unitPrice})</span>
                        <span><strong>₹${totalPrice}</strong></span>
                    </div>
                    <div style="border-top: 1px solid #0ea5e9; padding-top: 0.5rem; display: flex; justify-content: space-between; font-size: 1.1rem;">
                        <span><strong>Total Amount:</strong></span>
                        <span><strong>₹${totalPrice}</strong></span>
                    </div>
                `;
                priceDisplay.style.display = 'block';
            } else {
                priceDisplay.style.display = 'none';
            }
        }

        // Add event listeners for price calculation
        document.getElementById('product').addEventListener('change', updatePrice);
        document.getElementById('quantity').addEventListener('input', updatePrice);

        // Calculate price on page load if form data exists
        document.addEventListener('DOMContentLoaded', function() {
            updatePrice();
        });

        // Form validation enhancement
        document.querySelector('form').addEventListener('submit', function(e) {
            const mobile = document.getElementById('mobile').value;
            const mobilePattern = /^[6-9]\d{9}$/;

            if (!mobilePattern.test(mobile)) {
                e.preventDefault();
                alert('Please enter a valid 10-digit mobile number starting with 6, 7, 8, or 9');
                return false;
            }
        });
    </script>
</body>
</html>
