# Generated by Django 3.2 on 2025-07-04 14:37

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('mobile', models.CharField(max_length=15)),
                ('address', models.TextField()),
                ('product', models.CharField(choices=[('motha kendai', 'Motha Kendai'), ('netthili', 'Netthili'), ('vaalai', 'Vaalai'), ('goa netthili', 'Goa Netthili'), ('yeera', 'Yeera')], max_length=50)),
                ('quantity', models.PositiveIntegerField()),
                ('paid', models.BooleanField(default=False)),
            ],
        ),
    ]
