from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator

class Order(models.Model):
    PRODUCT_CHOICES = [
        ('motha kendai', '<PERSON><PERSON>'),
        ('netthili', 'Netthili'),
        ('vaalai', 'Vaalai'),
        ('goa netthili', 'Goa Netthili'),
        ('yeera', 'Yeera'),
    ]

    PAYMENT_METHOD_CHOICES = [
        ('google_pay', 'Google Pay'),
        ('phonepe', 'PhonePe'),
        ('paytm', 'Paytm'),
        ('upi', 'UPI'),
        ('cash_on_delivery', 'Cash on Delivery'),
        ('bank_transfer', 'Bank Transfer'),
    ]

    PAYMENT_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    # Product pricing configuration
    PRODUCT_PRICING = {
        'motha kendai': {
            'per_kg_rate': 699.00,
            'fixed_prices': {
                500: 199.00,  # 500g fixed price
                1000: 449.00,  # 1kg fixed price
            }
        },
        'netthili': {
            'per_kg_rate': 799.00,
            'fixed_prices': {
                500: 299.00,
                1000: 499.00,
            }
        },
        'vaalai': {
            'per_kg_rate': 799.00,
            'fixed_prices': {
                500: 299.00,
                1000: 499.00,
            }
        },
        'goa netthili': {
            'per_kg_rate': 899.00,
            'fixed_prices': {
                500: 499.00,
                1000: 699.00,
            }
        },
        'yeera': {
            'per_kg_rate': 899.00,
            'fixed_prices': {
                500: 499.00,
                1000: 699.00,
            }
        },
    }

    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=100)
    email = models.EmailField(help_text="Customer's email address")
    mobile = models.CharField(max_length=15)
    address = models.TextField()  # Keep for backward compatibility

    # New detailed delivery address fields
    street_name = models.CharField(max_length=200, blank=True, null=True, help_text="Street name and house number")
    place_name = models.CharField(max_length=100, blank=True, null=True, help_text="Area/locality name")
    city = models.CharField(max_length=100, blank=True, null=True, help_text="City name")
    state = models.CharField(max_length=100, blank=True, null=True, help_text="State name")
    pin_code = models.CharField(max_length=10, blank=True, null=True, help_text="PIN/ZIP code")

    product = models.CharField(max_length=50, choices=PRODUCT_CHOICES)
    quantity_grams = models.PositiveIntegerField(help_text="Quantity in grams (100g to 10kg)")
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES, default='cash_on_delivery')
    payment_status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending')
    transaction_id = models.CharField(max_length=100, blank=True, null=True)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    paid = models.BooleanField(default=False)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        # Calculate unit price and total amount based on product and quantity
        if self.product and self.quantity_grams:
            self.unit_price = self.calculate_price_for_quantity(self.product, self.quantity_grams)
            self.total_amount = self.unit_price
        super().save(*args, **kwargs)

    def get_quantity_display(self):
        """Get human-readable quantity display"""
        if self.quantity_grams >= 1000:
            kg = self.quantity_grams / 1000
            if kg == int(kg):
                return f"{int(kg)}kg"
            else:
                return f"{kg:.1f}kg"
        else:
            return f"{self.quantity_grams}g"

    def get_quantity_in_kg(self):
        """Convert quantity to kg for calculation"""
        return self.quantity_grams / 1000

    def get_discount_amount(self):
        """Calculate discount amount compared to per-kg rate"""
        if self.product in self.PRODUCT_PRICING:
            per_kg_rate = self.PRODUCT_PRICING[self.product]['per_kg_rate']
            quantity_kg = self.get_quantity_in_kg()
            original_price = per_kg_rate * quantity_kg
            return max(0, original_price - float(self.unit_price))
        return 0

    @classmethod
    def calculate_price_for_quantity(cls, product, quantity_grams):
        """Calculate price for a specific product and quantity in grams"""
        if product not in cls.PRODUCT_PRICING:
            return 0

        pricing = cls.PRODUCT_PRICING[product]
        per_kg_rate = pricing['per_kg_rate']
        fixed_prices = pricing['fixed_prices']

        # Check for fixed prices first
        if quantity_grams in fixed_prices:
            return fixed_prices[quantity_grams]

        # Special case for 100g (divide per kg by 10)
        if quantity_grams == 100:
            return per_kg_rate / 10

        # For other quantities, use per gram calculation
        per_gram_rate = per_kg_rate / 1000
        return per_gram_rate * quantity_grams

    @classmethod
    def calculate_discount(cls, product, quantity_grams):
        """Calculate discount for a specific product and quantity"""
        if product not in cls.PRODUCT_PRICING:
            return 0

        per_kg_rate = cls.PRODUCT_PRICING[product]['per_kg_rate']
        actual_price = cls.calculate_price_for_quantity(product, quantity_grams)
        quantity_kg = quantity_grams / 1000
        original_price = per_kg_rate * quantity_kg

        return max(0, original_price - actual_price)

    @classmethod
    def get_original_price(cls, product, quantity_grams):
        """Get original price (per kg rate * quantity)"""
        if product not in cls.PRODUCT_PRICING:
            return 0

        per_kg_rate = cls.PRODUCT_PRICING[product]['per_kg_rate']
        quantity_kg = quantity_grams / 1000
        return per_kg_rate * quantity_kg

    def get_full_address(self):
        """Get formatted full delivery address from individual fields"""
        address_parts = []
        if self.street_name:
            address_parts.append(self.street_name)
        if self.place_name:
            address_parts.append(self.place_name)
        if self.city:
            address_parts.append(self.city)
        if self.state:
            address_parts.append(self.state)
        if self.pin_code:
            address_parts.append(self.pin_code)

        if address_parts:
            return ", ".join(address_parts)
        return self.address  # Fallback to old address field

    def __str__(self):
        return f"{self.name} - {self.product} {self.get_quantity_display()} (₹{self.total_amount})"


class ProductReview(models.Model):
    """Model for product reviews and ratings"""

    PRODUCT_CHOICES = [
        ('motha kendai', 'Motha Kendai'),
        ('netthili', 'Netthili'),
        ('vaalai', 'Vaalai'),
        ('goa netthili', 'Goa Netthili'),
        ('yeera', 'Yeera'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='product_reviews')
    product = models.CharField(max_length=50, choices=PRODUCT_CHOICES)
    rating = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text="Rating from 1 to 5 stars"
    )
    review_text = models.TextField(blank=True, null=True, help_text="Optional review text")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'product')  # Ensure one review per user per product
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.get_product_display()} ({self.rating}★)"

    def get_star_display(self):
        """Return star rating as visual stars"""
        return '★' * self.rating + '☆' * (5 - self.rating)

    @classmethod
    def get_average_rating(cls, product):
        """Get average rating for a product"""
        reviews = cls.objects.filter(product=product)
        if reviews.exists():
            return reviews.aggregate(models.Avg('rating'))['rating__avg']
        return 0

    @classmethod
    def get_review_count(cls, product):
        """Get total number of reviews for a product"""
        return cls.objects.filter(product=product).count()

    @classmethod
    def get_star_display_for_product(cls, product):
        """Get visual star display for a product's average rating"""
        avg_rating = cls.get_average_rating(product)
        if avg_rating:
            full_stars = int(avg_rating)
            half_star = 1 if avg_rating - full_stars >= 0.5 else 0
            empty_stars = 5 - full_stars - half_star

            stars = '★' * full_stars
            if half_star:
                stars += '☆'
            stars += '☆' * empty_stars
            return stars
        return '☆☆☆☆☆'
