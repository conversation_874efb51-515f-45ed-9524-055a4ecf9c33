from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator

class Order(models.Model):
    PRODUCT_CHOICES = [
        ('motha kendai', '<PERSON><PERSON>'),
        ('netthili', 'Netthili'),
        ('vaalai', 'Vaalai'),
        ('goa netthili', 'Goa Netthili'),
        ('yeera', 'Yeera'),
    ]

    PAYMENT_METHOD_CHOICES = [
        ('google_pay', 'Google Pay'),
        ('phonepe', 'PhonePe'),
        ('paytm', 'Paytm'),
        ('upi', 'UPI'),
        ('cash_on_delivery', 'Cash on Delivery'),
        ('bank_transfer', 'Bank Transfer'),
    ]

    PAYMENT_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    # Product prices (per kg)
    PRODUCT_PRICES = {
        'motha kendai': 450,
        'netthili': 400,
        'vaalai': 300,
        'goa netthili': 600,
        'yeera': 500,
    }

    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=100)
    mobile = models.CharField(max_length=15)
    address = models.TextField()  # Keep for backward compatibility

    # New detailed delivery address fields
    street_name = models.CharField(max_length=200, blank=True, null=True, help_text="Street name and house number")
    place_name = models.CharField(max_length=100, blank=True, null=True, help_text="Area/locality name")
    city = models.CharField(max_length=100, blank=True, null=True, help_text="City name")
    state = models.CharField(max_length=100, blank=True, null=True, help_text="State name")
    pin_code = models.CharField(max_length=10, blank=True, null=True, help_text="PIN/ZIP code")

    product = models.CharField(max_length=50, choices=PRODUCT_CHOICES)
    quantity = models.PositiveIntegerField()
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES, default='cash_on_delivery')
    payment_status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending')
    transaction_id = models.CharField(max_length=100, blank=True, null=True)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    paid = models.BooleanField(default=False)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        # Calculate total amount based on product and quantity
        if self.product in self.PRODUCT_PRICES:
            self.total_amount = self.PRODUCT_PRICES[self.product] * self.quantity
        super().save(*args, **kwargs)

    def get_product_price(self):
        return self.PRODUCT_PRICES.get(self.product, 0)

    def get_full_address(self):
        """Get formatted full delivery address from individual fields"""
        address_parts = []
        if self.street_name:
            address_parts.append(self.street_name)
        if self.place_name:
            address_parts.append(self.place_name)
        if self.city:
            address_parts.append(self.city)
        if self.state:
            address_parts.append(self.state)
        if self.pin_code:
            address_parts.append(self.pin_code)

        if address_parts:
            return ", ".join(address_parts)
        return self.address  # Fallback to old address field

    def __str__(self):
        return f"{self.name} - {self.product} (₹{self.total_amount})"


class ProductReview(models.Model):
    """Model for product reviews and ratings"""

    PRODUCT_CHOICES = [
        ('motha kendai', 'Motha Kendai'),
        ('netthili', 'Netthili'),
        ('vaalai', 'Vaalai'),
        ('goa netthili', 'Goa Netthili'),
        ('yeera', 'Yeera'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='product_reviews')
    product = models.CharField(max_length=50, choices=PRODUCT_CHOICES)
    rating = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text="Rating from 1 to 5 stars"
    )
    review_text = models.TextField(blank=True, null=True, help_text="Optional review text")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'product')  # Ensure one review per user per product
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.get_product_display()} ({self.rating}★)"

    def get_star_display(self):
        """Return star rating as visual stars"""
        return '★' * self.rating + '☆' * (5 - self.rating)

    @classmethod
    def get_average_rating(cls, product):
        """Get average rating for a product"""
        reviews = cls.objects.filter(product=product)
        if reviews.exists():
            return reviews.aggregate(models.Avg('rating'))['rating__avg']
        return 0

    @classmethod
    def get_review_count(cls, product):
        """Get total number of reviews for a product"""
        return cls.objects.filter(product=product).count()

    @classmethod
    def get_star_display_for_product(cls, product):
        """Get visual star display for a product's average rating"""
        avg_rating = cls.get_average_rating(product)
        if avg_rating:
            full_stars = int(avg_rating)
            half_star = 1 if avg_rating - full_stars >= 0.5 else 0
            empty_stars = 5 - full_stars - half_star

            stars = '★' * full_stars
            if half_star:
                stars += '☆'
            stars += '☆' * empty_stars
            return stars
        return '☆☆☆☆☆'
