# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
# Translators:
# , 2013
msgid ""
msgstr ""
"Project-Id-Version: django-allauth\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2024-10-26 18:15+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Turkish <https://hosted.weblate.org/projects/allauth/django-"
"allauth/tr/>\n"
"Language: tr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 5.8.2-dev\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "Bu hesap şu anda etkin durumda değil."

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "Birincil e-posta adresinizi kaldıramazsınız."

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr ""
"Bu e-posta adresi, halihazırda bu hesap ile ilişkilendirilmiş haldedir."

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "Girdiğiniz e-posta adresi ve/veya şifre doğru değil."

#: account/adapter.py:66
#, fuzzy
#| msgid "The username and/or password you specified are not correct."
msgid "The phone number and/or password you specified are not correct."
msgstr "Girdiğiniz kullanıcı adı ve/veya şifre doğru değil."

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "Bu e-posta adresiyle bir kullanıcı zaten kayıtlı."

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "Lütfen mevcut şifrenizi giriniz."

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "Hatalı kod."

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "Hatalı şifre."

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "Geçersiz veya süresi geçmiş anahtar."

#: account/adapter.py:73
#, fuzzy
#| msgid "Invalid token."
msgid "Invalid login."
msgstr "Geçersiz jeton."

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "Şifre sıfırlama kodu geçersiz."

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "%d adetten fazla e-posta adresi ekleyemezsiniz."

#: account/adapter.py:76
#, fuzzy
#| msgid "A user is already registered with this email address."
msgid "A user is already registered with this phone number."
msgstr "Bu e-posta adresiyle bir kullanıcı zaten kayıtlı."

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr "Çok fazla hatalı giriş yapıldı. Lütfen daha sonra tekrar deneyin."

#: account/adapter.py:80
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The email address is not assigned to any user account."
msgstr "Bu e-posta adresi hiçbir kullanıcı hesabıyla ilişkili değil"

#: account/adapter.py:81
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The phone number is not assigned to any user account."
msgstr "Bu e-posta adresi hiçbir kullanıcı hesabıyla ilişkili değil"

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Birincil e-posta adresinizin doğrulanması gerekmektedir."

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr "Bu kullanıcı adı kullanılamaz. Lütfen başka bir kullanıcı adı deneyin."

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "Girdiğiniz kullanıcı adı ve/veya şifre doğru değil."

#: account/adapter.py:92
msgid "Please select only one."
msgstr ""

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr ""

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr ""

#: account/adapter.py:778
msgid "Use your password"
msgstr "Şifrenizi kullanın"

#: account/adapter.py:787
msgid "Use authenticator app or code"
msgstr "Doğrulayıcı uygulaması veya kodu kullan"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
msgid "Use a security key"
msgstr "Güvenlik anahtarı kullan"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "Seçili e-posta adreslerini doğrulanmış olarak işaretle"

#: account/apps.py:11
msgid "Accounts"
msgstr "Hesaplar"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr ""

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr ""

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "Her sefer için aynı şifreyi girmelisiniz."

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "Şifre"

#: account/forms.py:100
msgid "Remember Me"
msgstr "Beni Hatırla"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "E-posta adresi"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "E-posta"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "Kullanıcı adı"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "Giriş Yap"

#: account/forms.py:154
#, fuzzy
#| msgid "Username or email"
msgid "Username, email or phone"
msgstr "Kullanıcı adı ya da e-posta adresi"

#: account/forms.py:156
msgid "Username or email"
msgstr "Kullanıcı adı ya da e-posta adresi"

#: account/forms.py:158
#, fuzzy
#| msgid "Username or email"
msgid "Username or phone"
msgstr "Kullanıcı adı ya da e-posta adresi"

#: account/forms.py:160
#, fuzzy
#| msgid "Email (optional)"
msgid "Email or phone"
msgstr "E-posta adresi (zorunlu değil)"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "Şifrenizi mi unuttunuz?"

#: account/forms.py:334
msgid "Email (again)"
msgstr "E-posta adresi (tekrar)"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "E-posta adresi doğrulama"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "E-posta adresi (zorunlu değil)"

#: account/forms.py:361
#, fuzzy
#| msgid "Email (optional)"
msgid "Username (optional)"
msgstr "E-posta adresi (zorunlu değil)"

#: account/forms.py:435
msgid "You must type the same email each time."
msgstr "Her sefer için aynı e-posta adresini girmelisiniz."

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "Şifre (tekrar)"

#: account/forms.py:645
msgid "Current Password"
msgstr "Mevcut Şifre"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "Yeni Şifre"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "Yeni Şifre (tekrar)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "Kod"

#: account/models.py:26
msgid "user"
msgstr "kullanıcı"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "e-posta adresi"

#: account/models.py:34
msgid "verified"
msgstr "doğrulanmış"

#: account/models.py:35
msgid "primary"
msgstr "birincil"

#: account/models.py:41
msgid "email addresses"
msgstr "e-posta adresleri"

#: account/models.py:151
msgid "created"
msgstr "oluşturuldu"

#: account/models.py:152
msgid "sent"
msgstr "gönderildi"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "anahtar"

#: account/models.py:158
msgid "email confirmation"
msgstr "e-posta onayı"

#: account/models.py:159
msgid "email confirmations"
msgstr "e-posta onayları"

#: headless/apps.py:7
msgid "Headless"
msgstr "Gözetimsiz"

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"İki faktörlü kimlik doğrulama ile korunan bir hesaba e-posta adresi "
"ekleyemezsiniz."

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr "İki faktörlü kimlik doğrulamayı devre dışı bırakamazsınız."

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr ""
"İki faktörlü kimlik doğrulamayı aktif hale getirmeden kurtarma kodu "
"oluşturamazsınız."

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"Doğrulanmış bir e-posta adresiniz olmadığı sürece iki faktörlü kimlik "
"doğrulamayı etkinleştiremezsiniz."

#: mfa/adapter.py:141
msgid "Master key"
msgstr "Ana anahtar"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr "Yedek anahtar"

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr "{number} no.lu Anahtar"

#: mfa/apps.py:9
msgid "MFA"
msgstr "MFA(Çok Faktörlü Kimlik Doğrulama)"

#: mfa/models.py:24
msgid "Recovery codes"
msgstr "Kurtarma kodları"

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr "TOTP(Zaman Tabanlı Tek Seferlik Parola) Doğrulayıcısı"

#: mfa/models.py:26
msgid "WebAuthn"
msgstr "WebAuthn"

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr "Doğrulayıcı kodu"

#: mfa/webauthn/forms.py:59
msgid "Passwordless"
msgstr "Şifresiz"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""
"Şifresiz işlemi etkinleştirmek sadece bu anahtarı kullanarak giriş yapmanıza "
"olanak sağlar fakat biyometri veya PIN koruması gibi ek gereksinimleri "
"zorunlu kılar."

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Bu e-posta adresi ile kayıtlı bir hesap bulunmaktadır. Lütfen önce bu hesaba "
"giriş yapıp daha sonra %s adlı hesabınızı bağlayınız."

#: socialaccount/adapter.py:39
msgid "Invalid token."
msgstr "Geçersiz jeton."

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "Hesabınızda belirlenmiş herhangi bir şifre bulunmamaktadır."

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "Hesabınızın doğrulanmış e-posta adresi yok."

#: socialaccount/adapter.py:43
msgid "You cannot disconnect your last remaining third-party account."
msgstr "Kalan son üçüncü taraf hesabınızın bağlantısını kesemezsiniz."

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The third-party account is already connected to a different account."
msgstr "Bu üçüncü taraf hesabı halihazırda farklı bir hesaba bağlıdır."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "Sosyal Hesaplar"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "sağlayıcı"

#: socialaccount/models.py:52
msgid "provider ID"
msgstr "tedarikçi kimlik numarası"

#: socialaccount/models.py:56
msgid "name"
msgstr "isim"

#: socialaccount/models.py:58
msgid "client id"
msgstr "istemci kimlik numarası"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "Uygulama kimlik numarası veya tüketici anahtarı"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "gizli anahtar"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr "API anahtarı, istemci anahtarı veya tüketici anahtarı"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "Anahtar"

#: socialaccount/models.py:81
msgid "social application"
msgstr "sosyal medya uygulaması"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "sosyal medya uygulamaları"

#: socialaccount/models.py:117
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:119
msgid "last login"
msgstr "son giriş"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "katıldığı tarih"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "ek veri"

#: socialaccount/models.py:125
msgid "social account"
msgstr "sosyal hesap"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "sosyal hesaplar"

#: socialaccount/models.py:160
msgid "token"
msgstr "jeton"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) veya access token (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "jeton anahtarı"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) veya refresh token (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "geçersiz olma tarihi"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "sosyal medya uygulaması jetonu"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "sosyal medya uygulaması jetonları"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Geçersiz profil bilgisi"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
msgid "Login"
msgstr "Giriş Yap"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr "Iptal"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr ""
"\"%s\"'dan istek jetonu temin edilirken geçersiz yanıt alındı. Yanıt %s idi."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "\"%s\"'dan erişim kodu alınırken geçersiz cevap alındı."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "\"%s\" için hiçbir talep kodu kaydedilmedi."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "\"%s\" için hiçbir erişim kodu kaydedilmedi."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "\"%s\"'daki özel kaynaklara erişim yok."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "\"%s\"'dan talep kodu alınırken geçersiz cevap alındı."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Hesap Etkin Değil"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "Bu hesap etkin değil."

#: templates/account/base_confirm_code.html:27
#, fuzzy, python-format
#| msgid ""
#| "We’ve sent a code to %(email_link)s. The code expires shortly, so please "
#| "enter it soon."
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr ""
"%(email_link)s adresine bir kod gönderdik. Gönderilen kodun süresi kısa "
"zamanda dolacağından dolayı lütfen hızlı davranınız."

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "Onayla"

#: templates/account/base_confirm_code.html:43
#, fuzzy
#| msgid "Request Code"
msgid "Request new code"
msgstr "İstek Kodu"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "Erişim Doğrulama"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr "Hesabınızı güvenilir hale getirmek için lütfen tekrar doğrulayın."

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr "Alternatif seçenekler"

#: templates/account/confirm_email_verification_code.html:5
msgid "Email Verification"
msgstr "E-Posta Onayı"

#: templates/account/confirm_email_verification_code.html:8
msgid "Enter Email Verification Code"
msgstr "E-Posta Onay Kodu Gir"

#: templates/account/confirm_email_verification_code.html:16
#, fuzzy
#| msgid "email address"
msgid "Use a different email address"
msgstr "e-posta adresi"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "Giriş Yap"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr "Giriş Kodunu Gir"

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Şifre Sıfırlama"

#: templates/account/confirm_password_reset_code.html:8
#, fuzzy
#| msgid "Password Reset"
msgid "Enter Password Reset Code"
msgstr "Şifre Sıfırlama"

#: templates/account/confirm_phone_verification_code.html:5
#, fuzzy
#| msgid "Re-send Verification"
msgid "Phone Verification"
msgstr "Tekrar Doğrula"

#: templates/account/confirm_phone_verification_code.html:8
#, fuzzy
#| msgid "Enter Email Verification Code"
msgid "Enter Phone Verification Code"
msgstr "E-Posta Onay Kodu Gir"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "E-posta Adresleri"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr "Şu e-posta adresleri hesabınızla ilişkilendirildi:"

#: templates/account/email.html:25
msgid "Verified"
msgstr "Doğrulanmış"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "Doğrulanmamış"

#: templates/account/email.html:34
msgid "Primary"
msgstr "Birincil"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "Birincil Yap"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "Tekrar Doğrula"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Kaldır"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "E-posta Adresi Ekle"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "E-posta Ekle"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "Seçilen e-posta adresini kaldırmak istediğinizden emin misiniz?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"Bu e-postayı, siz veya başka birisi aşağıdaki e-posta adresini kullanarak "
"kayıt olmaya\n"
"çalıştığı için alıyorsunuz:\n"
"\n"
"%(email)s\n"
"\n"
"Fakat, bu e-posta adresini kullanan bir hesap halihazırda mevcut "
"durumdadır.  Eğer unuttuysanız\n"
"hesabınızı kurtarmak için lütfen şifre yenileme işleminden\n"
"faydalanınız:\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "Hesap Halihazırda Mevcut"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "%(site_name)s'ten Merhaba!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"%(site_name)s'i kullandığınız için teşekkür ederiz!\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr "Bu e-postayı, hesabınızda bir değişiklik yapıldığı için alıyorsunuz:"

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""
"Bu değişiklikten haberdar değilseniz lütfen gerekli güvenlik önlemlerini "
"derhal alınız. Hesabınızdaki değişikliğin kaynağı alt kısımda "
"belirtilmiştir:\n"
"\n"
"- IP adresi: %(ip)s\n"
"- Tarayıcı: %(user_agent)s\n"
"- Tarih: %(timestamp)s"

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr ""
"%(from_email)s adlı e-posta adresiniz %(to_email)s şeklinde değiştirilmiştir."

#: templates/account/email/email_changed_subject.txt:3
msgid "Email Changed"
msgstr "E-posta Adresi Değiştirildi"

#: templates/account/email/email_confirm_message.txt:4
msgid "Your email has been confirmed."
msgstr "E-posta adresiniz onaylandı."

#: templates/account/email/email_confirm_subject.txt:3
msgid "Email Confirmation"
msgstr "E-Posta Adresi Onayı"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"Bu e-postayı, %(user_display)s adlı kullanıcı, e-posta adresinizi "
"%(site_domain)s üzerinde bir hesap oluşturmak adına kullandığı için "
"alıyorsunuz."

#: templates/account/email/email_confirmation_message.txt:7
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"E-posta onay kodunuz alt kısımda belirtilmiştir. Kodu, açık olan tarayıcı "
"sekmenize giriniz."

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr ""
"Bunun doğru olduğunu düşünüyorsanız %(activate_url)s adlı bağlantıyı ziyaret "
"ediniz."

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Lütfen E-posta Adresinizi Onaylayın"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr "%(deleted_email)s adlı e-posta adresi hesabınızdan kaldırılmıştır."

#: templates/account/email/email_deleted_subject.txt:3
msgid "Email Removed"
msgstr "E-Posta Adresi Kaldırıldı"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr ""
"Giriş kodunuz alt kısımda belirtilmiştir. Kodu, açık olan tarayıcı sekmenize "
"giriniz."

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr ""
"Alınan aksiyondan haberdar değilseniz bu e-postayı görmezden gelebilirsiniz."

#: templates/account/email/login_code_subject.txt:3
msgid "Sign-In Code"
msgstr "Giriş Kodu"

#: templates/account/email/password_changed_message.txt:4
msgid "Your password has been changed."
msgstr "Şifreniz değiştirildi."

#: templates/account/email/password_changed_subject.txt:3
msgid "Password Changed"
msgstr "Şifre Değiştirildi"

#: templates/account/email/password_reset_code_message.txt:5
#, fuzzy
#| msgid ""
#| "Your sign-in code is listed below. Please enter it in your open browser "
#| "window."
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Giriş kodunuz alt kısımda belirtilmiştir. Kodu, açık olan tarayıcı sekmenize "
"giriniz."

#: templates/account/email/password_reset_code_subject.txt:3
#, fuzzy
#| msgid "Password Reset"
msgid "Password Reset Code"
msgstr "Şifre Sıfırlama"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Bu e-postayı, siz veya başka biri, kullanıcı hesabınız için şifre sıfırlama "
"talebinde bulunduğu için alıyorsunuz.\n"
"Eğer şifre sıfırlama talebinde bulunan kişi siz değilseniz bu uyarıyı "
"görmezden gelebilirsiniz. Şifrenizi sıfırlamak için aşağıdaki bağlantıya "
"tıklayın."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "Kullanıcı adınızı unuttuysanız, kullanıcı adınız: %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "Şifre Sıfırlama E-postası"

#: templates/account/email/password_reset_message.txt:4
msgid "Your password has been reset."
msgstr "Şifreniz sıfırlandı."

#: templates/account/email/password_set_message.txt:4
msgid "Your password has been set."
msgstr "Şifreniz belirlendi."

#: templates/account/email/password_set_subject.txt:3
msgid "Password Set"
msgstr "Şifre Tanımlandı"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""
"Bu e-postayı, siz ya da başka biri %(email)s adlı e-posta adresini "
"kullanarak bir hesaba giriş yapmaya çalıştığı için alıyorsunuz. Ancak, giriş "
"yapmaya çalışılan hesaba dair veritabanımızda herhangi bir kayıt "
"bulunmamaktadır."

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr ""
"Eğer o kişi siz iseniz aşağıdaki linki kullanarak bir hesap "
"oluşturabilirsiniz."

#: templates/account/email/unknown_account_subject.txt:3
msgid "Unknown Account"
msgstr "Bilinmeyen Hesap"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "E-posta Adresi"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "Mevcut e-posta"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "Şuna değiştiriliyor"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr "E-posta adresiniz hala onay aşamasındadır."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "Değişikliği İptal Et"

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
msgid "Change to"
msgstr "Şuna değiştir"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
msgid "Change Email"
msgstr "E-posta Adresini Değiştir"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "E-posta Adresi Doğrula"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Lütfen <a href=\"mailto:%(email)s\">%(email)s</a> adlı e-posta adresinin "
"%(user_display)s kullanıcısına ait olduğunu onaylayın."

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr ""
"%(email)s adlı e-posta adresi başka bir hesap tarafından doğrulandığı için "
"onaylanamıyor."

#: templates/account/email_confirm.html:36
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Bu e-posta adresi onaylama bağlantısı sona ermiş veya geçersizdir. Lütfen <a "
"href=\"%(email_url)s\">yeni bir e-posta adresi doğrulama talebinde bulunun.</"
"a>."

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr ""
"Henüz bir hesap oluşturmadıysanız lütfen önce %(link)süye olun%(end_link)s."

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr "Geçiş anahtarı ile giriş yap"

#: templates/account/login.html:47 templates/account/request_login_code.html:9
#, fuzzy
#| msgid "Mail me a sign-in code"
msgid "Send me a sign-in code"
msgstr "Bana giriş kodu maili gönder"

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "Çıkış Yap"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "Çıkış yapmak istediğinize emin misiniz?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "(%(email)s) adlı birincil e-posta adresinizi kaldıramazsınız."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Doğrulama e-posta'sı %(email)s adresine gönderildi."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "%(email)s adresini onayladınız."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "%(email)s adresini sildiniz."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "%(name)s olarak başarıyla giriş yapıldı."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Çıkış yaptınız."

#: templates/account/messages/login_code_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been mailed to %(email)s."
msgid "A sign-in code has been sent to %(recipient)s."
msgstr "%(email)s e-posta adresine bir giriş kodu gönderildi."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Şifre başarıyla değiştirildi."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Şifre başarıyla belirlendi."

#: templates/account/messages/phone_verification_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been mailed to %(email)s."
msgid "A verification code has been sent to %(phone)s."
msgstr "%(email)s e-posta adresine bir giriş kodu gönderildi."

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr ""

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Birincil e-posta adresi tanımlandı."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "Şifre Değiştir"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "Şifrenizi mi unuttunuz?"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Şifrenizi mi unuttunuz? E-posta adresinizi aşağıya yazın ve size parolanızı "
"sıfırlamanıza imkan veren bir e-posta gönderelim."

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "Şifremi Sıfırla"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Şifrenizi sıfırlarken herhangi bir sorunla karşılaşırsanız lütfen bize "
"ulaşın."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Size bir e-posta gönderdik. E-postayı almadıysanız istenmeyen e-posta "
"klasörünüzü kontrol ediniz. Eğer birkaç dakika içinde elinize ulaşmaz ise "
"bizimle irtibata geçiniz."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Hatalı Anahtar"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Muhtemelen daha önce kullanıldığı için şifre sıfırlama bağlantısı geçersiz. "
"Lütfen <a href=\"%(passwd_reset_url)s\">yeni şifre sıfırlama</a> talebinde "
"bulunun."

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "Şifreniz değiştirildi."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "Şifre Belirle"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
#, fuzzy
#| msgid "Change to"
msgid "Change Phone"
msgstr "Şuna değiştir"

#: templates/account/phone_change.html:18
#, fuzzy
#| msgid "Current"
msgid "Current phone"
msgstr "Şu anki"

#: templates/account/phone_change.html:22
#, fuzzy
#| msgid "Your email address is still pending verification."
msgid "Your phone number is still pending verification."
msgstr "E-posta adresiniz hala onay aşamasındadır."

#: templates/account/reauthenticate.html:6
msgid "Enter your password:"
msgstr "Şifrenizi giriniz:"

#: templates/account/request_login_code.html:12
#, fuzzy
#| msgid ""
#| "You will receive an email containing a special code for a password-free "
#| "sign-in."
msgid "You will receive a special code for a password-free sign-in."
msgstr ""
"Şifresiz giriş yapabilmenizi sağlayan kodu içeren bir e-posta alacaksınız."

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr "İstek Kodu"

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr "Diğer giriş seçenekleri"

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Üye Ol"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "Üye Ol"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr ""
"Zaten hesabınız var mı? O zaman lütfen %(link)sgiriş yapın%(end_link)s."

#: templates/account/signup.html:39
msgid "Sign up using a passkey"
msgstr "Geçiş anahtarı kullanarak giriş yap"

#: templates/account/signup_by_passkey.html:8
msgid "Passkey Sign Up"
msgstr "Geçiş Anahtarı ile Üye Ol"

#: templates/account/signup_by_passkey.html:36
msgid "Other options"
msgstr "Diğer seçenekler"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Kayıt Kapalı"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "Üzgünüz, ancak kayıt şu anda kapalıdır."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Not"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "Halihazırda %(user_display)s olarak giriş yapmış durumdasınız."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Uyarı:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Şu anda tanımlı hiçbir e-posta adresiniz yok. Bildirim alabilmek, şifrenizi "
"sıfırlayabilmek ve benzeri eylemler için e-posta adresinizi mutlaka "
"eklemelisiniz."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "E-posta Adresinizi Doğrulayın"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Size, doğrulama amaçlı bir e-posta gönderdik. Kayıt olma aşamasını "
"tamamlamak için bağlantıyı ziyaret ediniz. Eğer doğrulama e-postasını gelen "
"kutunuzda göremiyorsanız istenmeyen e-posta klasörünüzü kontrol ediniz. Eğer "
"e-posta birkaç dakika içinde tarafınıza ulaşmaz ise bizimle irtibata geçiniz."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Sitenin bu kısmı kim olduğunuzu doğrulamanızı\n"
"gerektirmektedir. Bu sebeple, girdiğiniz e-posta adresinize sahip\n"
"olduğunuzu doğrulamanızı rica ediyoruz. "

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Doğrulama için size bir e-posta gönderdik.\n"
"Lütfen e-postadaki bağlantıyı ziyaret edin. Eğer e-postayı gelen kutunuzda "
"göremiyorsanız istenmeyen e-posta klasörünüzü kontrol edin. Eğer birkaç "
"dakika\n"
"içinde bu e-postayı almazsanız bize ulaşın."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Not:</strong> hala <a href=\"%(email_url)s\">e-posta adresinizi "
"değiştirebilirsiniz</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "Mesajlar:"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "Menü:"

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Hesap Bağlantıları"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "İki Faktörlü Kimlik Doğrulama"

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr "Oturumlar"

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""
"Hesabınız iki faktörlü kimlik doğrulama ile korunmaktadır. Lütfen bir "
"doğrulama kodu giriniz:"

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr "Yeni iki faktörlü kimlik doğrulama kurtarma kodu dizesi oluşturuldu."

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr "Yeni Kurtarma Kodları Oluşturuldu"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "Kimlik doğrulama uygulaması etkinleştirildi."

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr "Kimlik Doğrulama Uygulaması Etkinleştirildi"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "Kimlik doğrulama uygulaması devre dışı bırakıldı."

#: templates/mfa/email/totp_deactivated_subject.txt:3
msgid "Authenticator App Deactivated"
msgstr "Kimlik Doğrulama Uygulaması Devre Dışı Bırakıldı"

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr "Yeni bir güvenlik anahtarı eklendi."

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr "Güvenlik Anahtarı Eklendi"

#: templates/mfa/email/webauthn_removed_message.txt:4
msgid "A security key has been removed."
msgstr "Bir güvenlik anahtarı kaldırıldı."

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr "Güvenlik Anahtarı Kaldırıldı"

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "Kimlik Doğrulama Uygulaması"

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr "Kimlik doğrulama uygulaması aracılığı ile kimlik doğrulama etkin."

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr "Bir kimlik doğrulama uygulaması etkin değil."

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "Devre Dışı Bırak"

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "Etkinleştir"

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr "Güvenlik Anahtarları"

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] "%(count)s adet güvenlik anahtarı eklediniz."
msgstr[1] "%(count)s adet güvenlik anahtarı eklediniz."

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr "Hiçbir güvenlik anahtarı eklenmedi."

#: templates/mfa/index.html:62
msgid "Manage"
msgstr "Yönet"

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr "Ekle"

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "Kurtarma Kodları"

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
"%(total_count)s adet kurtarma kodu içerisinden %(unused_count)s sayıda "
"kurtarma kodu uygun durumda."
msgstr[1] ""
"%(total_count)s adet kurtarma kodu içerisinden %(unused_count)s sayıda "
"kurtarma kodu uygun durumda."

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr "Tanımlı kurtarma kodu bulunmamakta."

#: templates/mfa/index.html:96
msgid "View"
msgstr "İncele"

#: templates/mfa/index.html:102
msgid "Download"
msgstr "İndir"

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "Oluştur"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "Yeni bir kurtarma kodu dizesi oluşturuldu."

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr "Güvenlik anahtarı eklendi."

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr "Güvenlik anahtarı kaldırıldı."

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr "Bir kimlik doğrulama kodu gir:"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr "Hesabınız için yeni bir kurtarma kodu dizesi oluşturmak üzeresiniz."

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "Bu eylem, halihazırda mevcut olan kodlarınızı geçersiz kılacaktır."

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "Emin misin?"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "Kullanılmamış kodlar"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "Kodları indir"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "Yeni kod oluştur"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "Kimlik Doğrulama Uygulaması Etkinleştir"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"Hesabınızı iki faktörlü kimlik doğrulama ile korumak adına aşağıdaki QR "
"kodunu kimlik doğrulama uygulamanız ile tarayınız. Sonrasında uygulama "
"tarafından oluşturulan doğrulama kodunu aşağıdaki kısma giriniz."

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "Kimlik doğrulama anahtarı"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""
"Bu anahtarı saklayabilir ve kimlik doğrulama uygulamanızı ileriki bir "
"zamanda yeniden yüklemek için kullanabilirsiniz."

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "Kimlik Doğrulama Uygulamasını Devre Dışı Bırak"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""
"Kimlik doğrulama uygulaması tabanlı kimlik doğrulamayı devre dışı bırakmak "
"üzeresiniz. Bunu yapmak istediğinizden emin misiniz?"

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr ""

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr ""

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr ""

#: templates/mfa/webauthn/add_form.html:7
msgid "Add Security Key"
msgstr "Güvenlik Anahtarı Ekle"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
msgid "Remove Security Key"
msgstr "Güvenlik Anahtarı Kaldır"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
msgid "Are you sure you want to remove this security key?"
msgstr "Bu güvenlik anahtarını kaldırmak istediğinizden emin misiniz?"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr "Kullanım"

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr "Geçiş Anahtarı"

#: templates/mfa/webauthn/authenticator_list.html:37
msgid "Security key"
msgstr "Güvenlik anahtarı"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr "Bu anahtar, bir geçiş anahtarı olduğunu belirtmiyor."

#: templates/mfa/webauthn/authenticator_list.html:41
msgid "Unspecified"
msgstr "Belirtilmemiş"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr "%(created_at)s tarihinde eklendi"

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr "En son %(last_used)s tarihinde kullanıldı"

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr "Düzenle"

#: templates/mfa/webauthn/edit_form.html:7
msgid "Edit Security Key"
msgstr "Güvenlik Anahtarını Düzenle"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr "Kaydet"

#: templates/mfa/webauthn/signup_form.html:7
msgid "Create Passkey"
msgstr "Geçiş Anahtarı Oluştur"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""
"Hesabınız için geçiş anahtarı oluşturmak üzeresiniz. İleriki zamanlarda ek "
"anahtarlar ekleyebileceğiniz gibi anahtarları ayırt etmek adına açıklayıcı "
"isimler kullanabilirsiniz."

#: templates/mfa/webauthn/signup_form.html:21
msgid "Create"
msgstr "Oluştur"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr "Bu özellik JavaScript gerektirir."

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Third-Party Login Failure"
msgstr "Üçüncü Taraf Giriş Hatası"

#: templates/socialaccount/authentication_error.html:12
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr "Üçüncü taraf hesabınız ile giriş yapılırken bir hata meydana geldi."

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr ""
"Hesabınıza aşağıdaki üçüncü taraf hesaplar aracılığı ile giriş "
"yapabilirsiniz:"

#: templates/socialaccount/connections.html:46
msgid "You currently have no third-party accounts connected to this account."
msgstr "Bu hesap ile bağlantılı hiçbir üçüncü taraf hesap bulunmamakta."

#: templates/socialaccount/connections.html:50
msgid "Add a Third-Party Account"
msgstr "Üçüncü Taraf Hesap ekle"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr ""
"%(provider)s üzerinden bir üçüncü taraf hesap, hesabınız ile "
"ilişkilendirildi."

#: templates/socialaccount/email/account_connected_subject.txt:3
msgid "Third-Party Account Connected"
msgstr "Üçüncü Taraf Hesap İlişkilendirildi"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr ""
"%(provider)s üzerinden üçüncü taraf bir hesabın, hesabınız ile olan "
"bağlantısı kesildi."

#: templates/socialaccount/email/account_disconnected_subject.txt:3
msgid "Third-Party Account Disconnected"
msgstr "Üçüncü Taraf Hesap Bağlantısı Kesildi"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "%(provider)s Bağlantısı Kur"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr ""
"%(provider)s üzerinden yeni bir üçüncü taraf hesap bağlantısı kurmak "
"üzeresiniz."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "%(provider)s ile Giriş Yap"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr ""
"%(provider)s üzerinden üçüncü taraf bir hesap ile giriş yapmak üzeresiniz."

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr "İlerle"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Giriş İptal Edildi"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Mevcut hesaplarınızdan birisiyle sitemize girişinizi iptal etmeye karar "
"verdiniz. Eğer bu yanlışlıkla olduysa, lütfen devam edin: <a "
"href=\"%(login_url)s\">giriş yapın</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The third-party account has been connected."
msgstr "Üçüncü taraf hesap bağlantısı kuruldu."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The third-party account has been disconnected."
msgstr "Üçüncü taraf hesap bağlantısı kesildi."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"%(provider_name)s hesabınızı kullanarak %(site_name)s sitesine giriş yapmak "
"üzeresiniz.\n"
"Son aşama olarak lütfen formu doldurunuz:"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr "Veya üçüncü taraf bir uygulama kullan"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr "Diğer tüm oturumlardan çıkış yapıldı."

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr "Şu Tarihte Başladı"

#: templates/usersessions/usersession_list.html:24
msgid "IP Address"
msgstr "IP Adresi"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr "Tarayıcı"

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr "En son şu tarihte görüldü"

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "Şu anki"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr "Diğer Oturumlardan Çıkış Yap"

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr "Kullanıcı Oturumları"

#: usersessions/models.py:92
msgid "session key"
msgstr "oturum anahtarı"

#~ msgid "Account Connection"
#~ msgstr "Hesap Bağlantısı"

#, python-brace-format
#~ msgid "Password must be a minimum of {0} characters."
#~ msgstr "Parola en az {0} karakter olmalıdır."

#, python-format
#~ msgid ""
#~ "You are receiving this email because you or someone else has requested a\n"
#~ "password for your user account. However, we do not have any record of a "
#~ "user\n"
#~ "with email %(email)s in our database.\n"
#~ "\n"
#~ "This mail can be safely ignored if you did not request a password reset.\n"
#~ "\n"
#~ "If it was you, you can sign up for an account using the link below."
#~ msgstr ""
#~ "Bu e-postayı alıyorsunuz çünkü siz veya başka biri kullanıcı hesabınız "
#~ "için\n"
#~ "bir şifre talebinde bulundu. Ancak,veritabanımızda %(email)s e-posta "
#~ "adresi ile ilgili bir kayıt bulunmamaktadır.\n"
#~ "\n"
#~ "Eğer şifre sıfırlama talebinde bulunmadıysanız bu e-posta güvenle göz "
#~ "ardı edilebilir.\n"
#~ "\n"
#~ "Eğer talep sizden geldiyse, aşağıdaki bağlantıyı kullanarak bir hesap "
#~ "oluşturabilirsiniz."

#, fuzzy
#~| msgid "The following email addresses are associated with your account:"
#~ msgid "The following email address is associated with your account:"
#~ msgstr "Şu e-posta adresleri hesabınızla ilişkilendirildi:"

#, fuzzy
#~| msgid "Confirm Email Address"
#~ msgid "Change Email Address"
#~ msgstr "E-posta Adresi Doğrula"

#~ msgid "or"
#~ msgstr "ya da"

#~ msgid "change password"
#~ msgstr "parola değiştir"

#~ msgid "OpenID Sign In"
#~ msgstr "OpenID Girişi"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Bu e-post adresi başka bir hesap ile ilişkilendirilmiş."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Size bir e-posta gönderdik. Birkaç dakika içerisinde size ulaşmazsa "
#~ "lütfen bize ulaşın."

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "Girdiğiniz giriş bilgisi ve/veya parola doğru değil."

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr "Kullanıcı adları sadece harf, rakam ve @/./+/-/_ içerebilir."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "Bu kullanıcı adı alınmış durumda. Lütfen başka bir tane deneyiniz."

#, fuzzy
#~| msgid "Sign In"
#~ msgid "Shopify Sign In"
#~ msgstr "Giriş Yap"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "<a href=\"mailto:%(email)s\">%(email)s</a> adresinin %(user_display)s "
#~ "kullanıcısına ait olduğunu onayladınız."

#~ msgid "Thanks for using our site!"
#~ msgstr "Sitemizi kullandığınız için teşekkür ederiz!"

#~ msgid "Confirmation email sent to %(email)s"
#~ msgstr "Onay e-postası %(email)s adresine gönderildi."

#~ msgid "Delete Password"
#~ msgstr "Parola Sil"

#~ msgid ""
#~ "You may delete your password since you are currently logged in using "
#~ "OpenID."
#~ msgstr ""
#~ "Şu anda OpenID ile giriş yaptığınız için dilerseniz parolanızı "
#~ "silebilirsiniz."

#~ msgid "delete my password"
#~ msgstr "parolamı sil"

#~ msgid "Password Deleted"
#~ msgstr "Parola Silindi"
