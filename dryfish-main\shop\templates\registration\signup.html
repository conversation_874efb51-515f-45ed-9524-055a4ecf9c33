{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Account - Dry Fish Shop</title>
    <link rel="stylesheet" href="{% static 'shop/style.css' %}">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            position: relative;
            overflow-x: hidden;
        }

        /* Animated background elements */
        .bg-animation {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }

        .floating-shapes {
            position: absolute;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .floating-shapes:nth-child(1) {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
            width: 80px;
            height: 80px;
        }

        .floating-shapes:nth-child(2) {
            top: 20%;
            right: 10%;
            animation-delay: 2s;
            width: 120px;
            height: 120px;
        }

        .floating-shapes:nth-child(3) {
            bottom: 20%;
            left: 15%;
            animation-delay: 4s;
            width: 60px;
            height: 60px;
        }

        .floating-shapes:nth-child(4) {
            bottom: 30%;
            right: 20%;
            animation-delay: 1s;
            width: 90px;
            height: 90px;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .auth-container {
            position: relative;
            z-index: 1;
            width: 100%;
            max-width: 480px;
            margin: 0 auto;
        }

        .auth-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.2);
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .auth-card:hover {
            transform: translateY(-5px);
            box-shadow:
                0 35px 70px rgba(0, 0, 0, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.3);
        }

        .auth-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .auth-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            animation: pulse 2s infinite;
        }

        .auth-logo i {
            font-size: 2.5rem;
            color: white;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .auth-title {
            font-size: 2.2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #2c3e50 0%, #667eea 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
            line-height: 1.2;
        }

        .auth-subtitle {
            color: #64748b;
            font-size: 1.1rem;
            font-weight: 400;
            line-height: 1.5;
        }

        .form-group {
            margin-bottom: 1.8rem;
            position: relative;
        }

        .input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .input-icon {
            position: absolute;
            left: 1rem;
            color: #94a3b8;
            font-size: 1.1rem;
            z-index: 2;
            transition: color 0.3s ease;
        }

        .form-control {
            width: 100%;
            padding: 1.2rem 1rem 1.2rem 3rem;
            border: 2px solid #e2e8f0;
            border-radius: 16px;
            font-size: 1rem;
            font-weight: 500;
            background: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
            color: #1e293b;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .form-control:focus + .input-icon {
            color: #667eea;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.8rem;
            font-size: 0.95rem;
            letter-spacing: 0.5px;
        }

        .password-strength {
            margin-top: 0.5rem;
            height: 4px;
            background: #e2e8f0;
            border-radius: 2px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .strength-bar {
            height: 100%;
            width: 0%;
            background: linear-gradient(90deg, #ef4444, #f59e0b, #10b981);
            transition: width 0.3s ease;
            border-radius: 2px;
        }

        .btn-signup {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.3rem;
            border: none;
            border-radius: 16px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 2rem 0 1rem;
            position: relative;
            overflow: hidden;
            letter-spacing: 0.5px;
        }

        .btn-signup::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-signup:hover::before {
            left: 100%;
        }

        .btn-signup:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        }

        .btn-signup:active {
            transform: translateY(-1px);
        }

        .auth-links {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e2e8f0;
        }

        .auth-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
        }

        .auth-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -2px;
            left: 50%;
            background: #667eea;
            transition: all 0.3s ease;
        }

        .auth-link:hover::after {
            width: 100%;
            left: 0;
        }

        .auth-link:hover {
            color: #5a67d8;
        }

        .messages {
            margin-bottom: 2rem;
        }

        .message {
            padding: 1rem 1.2rem;
            border-radius: 12px;
            margin-bottom: 0.8rem;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            font-weight: 500;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.success {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            color: #065f46;
            border: 1px solid #10b981;
        }

        .message.error {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            color: #991b1b;
            border: 1px solid #ef4444;
        }

        .back-home {
            position: absolute;
            top: 2rem;
            left: 2rem;
            color: white;
            text-decoration: none;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.1);
            padding: 0.8rem 1.2rem;
            border-radius: 50px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .back-home:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-5px);
        }

        .password-requirements {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            font-size: 0.9rem;
        }

        .password-requirements h4 {
            margin: 0 0 1rem 0;
            color: #374151;
            font-size: 1rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .password-requirements ul {
            margin: 0;
            padding-left: 0;
            list-style: none;
            color: #64748b;
        }

        .password-requirements li {
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            padding: 0.3rem 0;
        }

        .password-requirements li::before {
            content: '✓';
            color: #10b981;
            font-weight: bold;
            width: 16px;
            height: 16px;
            background: #d1fae5;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
        }

        .progress-steps {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-bottom: 2rem;
        }

        .step {
            width: 40px;
            height: 4px;
            background: #e2e8f0;
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        .step.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        @media (max-width: 768px) {
            .auth-card {
                padding: 2rem;
                margin: 1rem;
                border-radius: 20px;
            }

            .auth-title {
                font-size: 1.8rem;
            }

            .auth-subtitle {
                font-size: 1rem;
            }

            .back-home {
                top: 1rem;
                left: 1rem;
                padding: 0.6rem 1rem;
                font-size: 0.9rem;
            }

            .floating-shapes {
                display: none;
            }
        }

        @media (max-width: 480px) {
            .auth-card {
                padding: 1.5rem;
            }

            .auth-title {
                font-size: 1.6rem;
            }
        }
    </style>
</head>
<body>
    <!-- Animated Background -->
    <div class="bg-animation">
        <div class="floating-shapes"></div>
        <div class="floating-shapes"></div>
        <div class="floating-shapes"></div>
        <div class="floating-shapes"></div>
    </div>

    <a href="/" class="back-home">
        <i class="fas fa-arrow-left"></i> Back to Home
    </a>

    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <div class="auth-logo">
                    <i class="fas fa-fish"></i>
                </div>
                <h1 class="auth-title">Create Your Account</h1>
                <p class="auth-subtitle">Join our community and start ordering premium dry fish</p>
            </div>

            <!-- Progress Steps -->
            <div class="progress-steps">
                <div class="step active"></div>
                <div class="step"></div>
                <div class="step"></div>
            </div>

            <!-- Messages -->
            {% if messages %}
                <div class="messages">
                    {% for message in messages %}
                        <div class="message {% if message.tags == 'error' %}error{% else %}success{% endif %}">
                            <i class="fas {% if message.tags == 'error' %}fa-exclamation-circle{% else %}fa-check-circle{% endif %}"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}

            <form method="post" id="signupForm">
                {% csrf_token %}
                <div class="form-group">
                    <label for="{{ form.username.id_for_label }}" class="form-label">Username</label>
                    <div class="input-wrapper">
                        {{ form.username }}
                        <i class="fas fa-user input-icon"></i>
                    </div>
                </div>

                <div class="form-group">
                    <label for="{{ form.password1.id_for_label }}" class="form-label">Password</label>
                    <div class="input-wrapper">
                        {{ form.password1 }}
                        <i class="fas fa-lock input-icon"></i>
                    </div>
                    <div class="password-strength">
                        <div class="strength-bar" id="strengthBar"></div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="{{ form.password2.id_for_label }}" class="form-label">Confirm Password</label>
                    <div class="input-wrapper">
                        {{ form.password2 }}
                        <i class="fas fa-shield-alt input-icon"></i>
                    </div>
                </div>

                <div class="password-requirements">
                    <h4><i class="fas fa-shield-check"></i> Password Requirements</h4>
                    <ul>
                        <li>At least 8 characters long</li>
                        <li>Cannot be too similar to your username</li>
                        <li>Cannot be a commonly used password</li>
                        <li>Cannot be entirely numeric</li>
                    </ul>
                </div>

                <button type="submit" class="btn-signup">
                    <i class="fas fa-user-plus"></i> Create My Account
                </button>
            </form>

            <div class="auth-links">
                <p>Already have an account?
                    <a href="{% url 'account_login' %}" class="auth-link">Sign in here</a>
                </p>
            </div>
        </div>
    </div>

    <script>
        // Password strength indicator
        document.addEventListener('DOMContentLoaded', function() {
            const passwordInput = document.getElementById('{{ form.password1.id_for_label }}');
            const strengthBar = document.getElementById('strengthBar');
            const steps = document.querySelectorAll('.step');

            if (passwordInput) {
                passwordInput.addEventListener('input', function() {
                    const password = this.value;
                    let strength = 0;

                    // Check password strength
                    if (password.length >= 8) strength += 25;
                    if (password.match(/[a-z]/)) strength += 25;
                    if (password.match(/[A-Z]/)) strength += 25;
                    if (password.match(/[0-9]/) || password.match(/[^a-zA-Z0-9]/)) strength += 25;

                    strengthBar.style.width = strength + '%';

                    // Update progress steps based on form completion
                    updateProgressSteps();
                });
            }

            // Update progress steps
            function updateProgressSteps() {
                const username = document.getElementById('{{ form.username.id_for_label }}').value;
                const password1 = document.getElementById('{{ form.password1.id_for_label }}').value;
                const password2 = document.getElementById('{{ form.password2.id_for_label }}').value;

                let activeSteps = 0;
                if (username.length > 0) activeSteps = 1;
                if (password1.length >= 8) activeSteps = 2;
                if (password2.length > 0 && password1 === password2) activeSteps = 3;

                steps.forEach((step, index) => {
                    if (index < activeSteps) {
                        step.classList.add('active');
                    } else {
                        step.classList.remove('active');
                    }
                });
            }

            // Add event listeners to all form inputs
            const formInputs = document.querySelectorAll('#signupForm input');
            formInputs.forEach(input => {
                input.addEventListener('input', updateProgressSteps);
            });

            // Add form classes to Django form fields
            const formControls = document.querySelectorAll('#signupForm input[type="text"], #signupForm input[type="password"]');
            formControls.forEach(input => {
                input.classList.add('form-control');
                input.setAttribute('autocomplete', 'off');
            });
        });
    </script>
</body>
</html>
