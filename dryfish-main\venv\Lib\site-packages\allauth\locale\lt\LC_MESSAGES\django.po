# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2024-04-20 22:02+0200\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < "
"11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? "
"1 : n % 1 != 0 ? 2: 3);\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "Šiuo metu ši paskyra yra neaktyvi."

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "Negalite pašalinti pagrindinio el. pašto."

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "Šis el. pašto adresas jau susietas su šia paskyra."

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "Pateiktas el. pašto adresas ir/arba slaptažodis yra neteisingi."

#: account/adapter.py:66
#, fuzzy
#| msgid "The username and/or password you specified are not correct."
msgid "The phone number and/or password you specified are not correct."
msgstr "Pateiktas naudotojo vardas ir/arba slaptažodis yra neteisingi."

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "Šiuo el. pašto adresu jau yra užsiregistravęs kitas naudotojas."

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "Prašome įvesti esamą jūsų slaptažodį."

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "Neteisingas kodas."

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "Neteisingas slaptažodis."

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "Neteisingas arba pasenęs raktas."

#: account/adapter.py:73
#, fuzzy
#| msgid "Invalid token."
msgid "Invalid login."
msgstr "Klaidinga atpažinimo žymė"

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "Neteisingas slaptažodžio atstatymo atpažinimo ženklas."

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "Negalima pridėti daugiau nei %d el. pašto adresų."

#: account/adapter.py:76
#, fuzzy
#| msgid "A user is already registered with this email address."
msgid "A user is already registered with this phone number."
msgstr "Šiuo el. pašto adresu jau yra užsiregistravęs kitas naudotojas."

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr "Per daug nepavykusių prisijungimo bandymų. Bandykite vėliau."

#: account/adapter.py:80
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The email address is not assigned to any user account."
msgstr "El. pašto adresas nėra susietas su jokia naudotojo paskyra"

#: account/adapter.py:81
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The phone number is not assigned to any user account."
msgstr "El. pašto adresas nėra susietas su jokia naudotojo paskyra"

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Pirminis el. pašto adresas turi būti patvirtintas."

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr ""
"Šis naudotojo vardas negalimas. Prašome pasirinkti kitą naudotojo vardą."

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "Pateiktas naudotojo vardas ir/arba slaptažodis yra neteisingi."

#: account/adapter.py:92
msgid "Please select only one."
msgstr ""

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr ""

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr ""

#: account/adapter.py:778
msgid "Use your password"
msgstr "Naudokite slaptažodį"

#: account/adapter.py:787
msgid "Use authenticator app or code"
msgstr "Naudokite autentifikacijos programą arba kodą"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
msgid "Use a security key"
msgstr "Naudokite saugumo raktą"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "Pažymėti pasirinktus el. pašto adresus, kaip patvirtintus"

#: account/apps.py:11
msgid "Accounts"
msgstr "Paskyros"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr ""

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr ""

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "Turite įvesti tą patį slaptažodį kiekvieną kartą."

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "Slaptažodis"

#: account/forms.py:100
msgid "Remember Me"
msgstr "Prisimink mane"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "El. pašto adresas"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "El. paštas"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "Naudotojo vardas"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "Prisijungimo vardas"

#: account/forms.py:154
#, fuzzy
#| msgid "Username or email"
msgid "Username, email or phone"
msgstr "Naudotojo vardas arba el. paštas"

#: account/forms.py:156
msgid "Username or email"
msgstr "Naudotojo vardas arba el. paštas"

#: account/forms.py:158
#, fuzzy
#| msgid "Username or email"
msgid "Username or phone"
msgstr "Naudotojo vardas arba el. paštas"

#: account/forms.py:160
#, fuzzy
#| msgid "Email (optional)"
msgid "Email or phone"
msgstr "El. paštas (neprivalomas)"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "Pamiršote slaptažodį?"

#: account/forms.py:334
msgid "Email (again)"
msgstr "El. paštas (pakartoti)"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "El. pašto patvirtinimas"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "El. paštas (neprivalomas)"

#: account/forms.py:361
#, fuzzy
#| msgid "Email (optional)"
msgid "Username (optional)"
msgstr "El. paštas (neprivalomas)"

#: account/forms.py:435
msgid "You must type the same email each time."
msgstr "Turite įvesti tą patį el. pašto adresą kiekvieną kartą."

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "Slaptažodis (pakartoti)"

#: account/forms.py:645
msgid "Current Password"
msgstr "Esamas slaptažodis"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "Naujas slaptažodis"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "Naujas slaptažodis (pakartoti)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "Kodas"

#: account/models.py:26
msgid "user"
msgstr "naudotojas"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "el. pašto adresas"

#: account/models.py:34
msgid "verified"
msgstr "patvirtintas"

#: account/models.py:35
msgid "primary"
msgstr "pirminis"

#: account/models.py:41
msgid "email addresses"
msgstr "el. pašto adresai"

#: account/models.py:151
msgid "created"
msgstr "sukurtas"

#: account/models.py:152
msgid "sent"
msgstr "išsiųstas"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "raktas"

#: account/models.py:158
msgid "email confirmation"
msgstr "el. pašto patvirtinimas"

#: account/models.py:159
msgid "email confirmations"
msgstr "el. pašto patvirtinimai"

#: headless/apps.py:7
msgid "Headless"
msgstr "Begalvis"

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"Jūs negalite pridėti el. pašto adreso į dviguba autentifikacija saugomą "
"paskyrą."

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr "Jūs negalite deaktyvuoti dvigubos autentifikacijos."

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr ""
"Jūs negalite generuoti atstatymo kodų be įjungtos dvigubos autentifikacijos."

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"Jūs negalite aktyvuoti dvigubos autentifikacijos kol nepatvirtinsite savo "
"el. pašto."

#: mfa/adapter.py:141
msgid "Master key"
msgstr "Pagrindinis raktas"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr "Atsarginis raktas"

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr "Rakto nr. {number}"

#: mfa/apps.py:9
msgid "MFA"
msgstr "MFA"

#: mfa/models.py:24
msgid "Recovery codes"
msgstr ""

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr ""

#: mfa/models.py:26
msgid "WebAuthn"
msgstr ""

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr ""

#: mfa/webauthn/forms.py:59
msgid "Passwordless"
msgstr "Be Slaptažodžio"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Paskyra su šiuo el. pašto adresu jau egzistuoja. Prašome pirmiausia "
"prisijungti prie tos paskyros ir tada prijunkite %s paskyrą."

#: socialaccount/adapter.py:39
msgid "Invalid token."
msgstr "Klaidinga atpažinimo žymė"

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "Jūsų paskyra neturi nustatyto slaptažodžio."

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "Jūsų paskyra neturi patvirtinto el. pašto adreso."

#: socialaccount/adapter.py:43
msgid "You cannot disconnect your last remaining third-party account."
msgstr "Negalite atjungti paskutinės trečiosios šalies paskyros."

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The third-party account is already connected to a different account."
msgstr "Trečiosios šalies paskyra jau yra prijungta prie kitos paskyros."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "Socialinės paskyros"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "tiekėjas"

#: socialaccount/models.py:52
msgid "provider ID"
msgstr "tiekėjo ID"

#: socialaccount/models.py:56
msgid "name"
msgstr "pavadinimas"

#: socialaccount/models.py:58
msgid "client id"
msgstr "kliento id"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "App ID arba consumer key"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "secret key"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr "API secret, client secret, arba consumer secret"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "Raktas"

#: socialaccount/models.py:81
msgid "social application"
msgstr "socialinė programėlė"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "socialinės programėlės"

#: socialaccount/models.py:117
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:119
msgid "last login"
msgstr "paskutinis prisijungimas"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "registracijos data"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "papildomi duomenys"

#: socialaccount/models.py:125
msgid "social account"
msgstr "socialinė paskyra"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "socialinės paskyros"

#: socialaccount/models.py:160
msgid "token"
msgstr "atpažinimo ženklas"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) arba prieigos atpažinimo ženklas (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "token secret"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr ""
"\"oauth_token_secret\" (OAuth1) arba atnaujintas atpažinimo ženklas (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "galiojimas"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "socialinės programėlės atpažinimo ženklas"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "socialinės programėlės atpažinimo ženklai"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Neteisingi profilio duomenys"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
msgid "Login"
msgstr "Prisijungti"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr "Atšaukti"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr ""
"Klaidingas atsakymas gaunant užklausos atpažinimo ženklą iš \"%s\". "
"Atsakymas buvo: %s."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Klaidingas atsakymas gaunant prieigos atpažinimo ženklą iš \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Nėra užklausos atpažinimo žymės šiam \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Nėra prieigos atpažinimo žymės šiam \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Nėra prieigos prie privataus resurso iš \"%s\"."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Klaidingas atsakymas gaunant užklausos atpažinimo ženklą iš \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Paskyra neaktyvi"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "Ši paskyra neaktyvi."

#: templates/account/base_confirm_code.html:27
#, fuzzy, python-format
#| msgid ""
#| "We’ve sent a code to %(email_link)s. The code expires shortly, so please "
#| "enter it soon."
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr ""
"Į %(email_link)s išsiuntėme kodą. Kodo galiojimas greitai pasibaigs, tas "
"įrašykite jį kuo greičiau."

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "Patvirtinti"

#: templates/account/base_confirm_code.html:43
#, fuzzy
#| msgid "Request Code"
msgid "Request new code"
msgstr "Prašyti Kodo"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "Patvirtinkite Prieigą"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr "Prašome dar kartą autentifikuotis, kad apsaugotumete savo paskyrą."

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr "Kiti pasirinkimai"

#: templates/account/confirm_email_verification_code.html:5
msgid "Email Verification"
msgstr "El. Pašto Patvirtinimas"

#: templates/account/confirm_email_verification_code.html:8
msgid "Enter Email Verification Code"
msgstr "Įrašykite Teisingą Patvirtinimo Kodą"

#: templates/account/confirm_email_verification_code.html:16
#, fuzzy
#| msgid "email address"
msgid "Use a different email address"
msgstr "el. pašto adresas"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "Prisijungti"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr "Įrašykite Prisijungimo Kodą"

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Slaptažodžio atstatymas"

#: templates/account/confirm_password_reset_code.html:8
#, fuzzy
#| msgid "Password Reset"
msgid "Enter Password Reset Code"
msgstr "Slaptažodžio atstatymas"

#: templates/account/confirm_phone_verification_code.html:5
#, fuzzy
#| msgid "Re-send Verification"
msgid "Phone Verification"
msgstr "Pakartotinai siųsti patvirtinimą"

#: templates/account/confirm_phone_verification_code.html:8
#, fuzzy
#| msgid "Enter Email Verification Code"
msgid "Enter Phone Verification Code"
msgstr "Įrašykite Teisingą Patvirtinimo Kodą"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "El. pašto adresai"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr "Šie el. pašto adresas yra susieti su jūsų paskyra:"

#: templates/account/email.html:25
msgid "Verified"
msgstr "Patvirtintas"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "Nepatvirtintas"

#: templates/account/email.html:34
msgid "Primary"
msgstr "Pirminis"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "Padaryti pirminiu"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "Pakartotinai siųsti patvirtinimą"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Šalinti"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "Pridėti el. pašto adresą"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "Pridėti el. paštą"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "Ar tikrai norite ištrinti pasirinktą el. pašto adresą?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"Jūs gaunate šį laišką, nes kažkas bandė prisiregistruoti\n"
" naudojantis :\n"
"\n"
"%(email)s\n"
"\n"
"Paskyra su šiuo el. pašto adresu egzistuoja. Jei užmiršote\n"
" apie tai, prašome naudotis užmiršto slaptažodžio procedūra, kad "
"atstatytumėte\n"
"jūsų paskyrą:\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "Pakyra Jau Egzistuoja"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Ačiū nuo %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Ačiū, kad naudojate %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr "Jūs gaunate šį laišką, nes šie pakitimai buvo padaryti jūsų paskyrai:"

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""
"Jei neatpažįstate šių pakitimų, imkitės saugumo užtikrinimo veiksmų. "
"Paskyros pakitimai buvo įvykdyti iš:\n"
"\n"
"- IP adreses: %(ip)s\n"
"- Naršyklė: %(user_agent)s\n"
"- Data: %(timestamp)s"

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr ""
"Jūsų el. pašto adresas buvo pakeistas iš %(from_email)s į %(to_email)s."

#: templates/account/email/email_changed_subject.txt:3
msgid "Email Changed"
msgstr "El. Paštas Pasikeitė"

#: templates/account/email/email_confirm_message.txt:4
msgid "Your email has been confirmed."
msgstr "Jūsų el. paštas patvirtintas."

#: templates/account/email/email_confirm_subject.txt:3
msgid "Email Confirmation"
msgstr "El. Pašto Patvirtinimas"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"Jūs gavote šį laišką, kadangi naudotojas %(user_display)s iš %(site_domain)s "
"prijungė šį el. pašto adresą prie savo paskyros."

#: templates/account/email/email_confirmation_message.txt:7
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Jūsų el. pašto patvirtinimo kodas yra nurodytas žemiau. Prašome įvesti jį į "
"atidarytos naršklės langą."

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr "Patvirtinkite, kad tai yra teisinga, eidami į %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Prašome patvirtinti savo el. pašto adresą"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr "El. pašto adresas %(deleted_email)s buvo pašalintas iš jūsų paskyros."

#: templates/account/email/email_deleted_subject.txt:3
msgid "Email Removed"
msgstr "El. Paštas Pašalintas"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr ""
"Jūsų el. pašto patvirtinimo kodas yra nurodytas žemiau. Prašome įvesti jį į "
"atidarytos naršyklės langą."

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr "Šis laiškas gali būti ignoruojamas jei šio veiksmo neiniciavote."

#: templates/account/email/login_code_subject.txt:3
msgid "Sign-In Code"
msgstr "Prisijungimo Kodas"

#: templates/account/email/password_changed_message.txt:4
msgid "Your password has been changed."
msgstr "Jūsų slaptažodis pakeistas."

#: templates/account/email/password_changed_subject.txt:3
msgid "Password Changed"
msgstr "Slaptažodis Pakeistas"

#: templates/account/email/password_reset_code_message.txt:5
#, fuzzy
#| msgid ""
#| "Your sign-in code is listed below. Please enter it in your open browser "
#| "window."
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Jūsų el. pašto patvirtinimo kodas yra nurodytas žemiau. Prašome įvesti jį į "
"atidarytos naršyklės langą."

#: templates/account/email/password_reset_code_subject.txt:3
#, fuzzy
#| msgid "Password Reset"
msgid "Password Reset Code"
msgstr "Slaptažodžio atstatymas"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Jūs gavote šį laišką, kadangi jūs arba kažkas kitas pateikė slaptažodžio "
"keitimo užklausą paskyrai susietai su šiuo el. pašto adresu iš "
"%(site_domain)s.\n"
"Jei jūs neteikėte slaptažodžio keitimo užklausos, galite ignoruoti šį "
"laišką. Kad pakeistumėte savo slaptažodį sekite žemiau esančią nuorodą."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "Primename, kad jūsų naudotojo vardas yra %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "Slaptažodžio keitimo el. paštas"

#: templates/account/email/password_reset_message.txt:4
msgid "Your password has been reset."
msgstr "Jūsų slaptažodis atstatytas."

#: templates/account/email/password_set_message.txt:4
msgid "Your password has been set."
msgstr "Jūsų slaptažodis nustatytas."

#: templates/account/email/password_set_subject.txt:3
msgid "Password Set"
msgstr "Slaptažodžio Nustatymas"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""
"Jūs gaunate šį laišką, nes kažkas bandė prisijungti prie paskyros su el. "
"pašto adresu %(email)s, tačiau tokios paskyros duombazėje nėra."

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr "Jei tai buvote jūs, registruotis galite sekdami nuorodą apačioje."

#: templates/account/email/unknown_account_subject.txt:3
msgid "Unknown Account"
msgstr "Nežinoma Paskyra"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "El. Pašto Adresas"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "Esamas el. pašto adresas"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "Keičiama į"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr "Jūsų el. pašto adresas dar vis laukia patvirtinimo."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "Atšaukti Keitimą"

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
msgid "Change to"
msgstr "Pakeista į"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
msgid "Change Email"
msgstr "Keisti El. Paštas Adresą"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Patvirtinkite el. pašto adresą"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Prašome patvirtinti, kad <a href=\"mailto:%(email)s\">%(email)s</a> yra "
"%(user_display)s el. pašto adresas."

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr ""
"%(email)s nepatvirtintas, nes šį el. paštą jau patvirtino kita paskyra."

#: templates/account/email_confirm.html:36
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Šios el. pašto patvirtinimo nuorodos galiojimas baigėsi arba nuoroda yra "
"klaidinga. Prašome <a href=\"%(email_url)s\">pateikti naują el. pašto "
"patvirtinimo užklausimą</a>."

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr ""
"Jei dar nesusikūrėte paskyros, tuomet prašome pirmiausia "
"%(link)ssusikurti%(end_link)s."

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr "Prisijunkite su prieigos raktu"

#: templates/account/login.html:47 templates/account/request_login_code.html:9
#, fuzzy
#| msgid "Mail me a sign-in code"
msgid "Send me a sign-in code"
msgstr "Atsiųsti prisijungimo kodą"

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "Atsijungti"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "Ar tikrai norite atsijungti?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Negalite ištrinti pirminio (%(email)s) el. pašto adreso."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Patvirtinimo laiškas išsiųstas adresu %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "%(email)s patvirtintas."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "%(email)s el. pašto adresas ištrintas."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Sėkmingai prisijungėte kaip %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Atsijungėte."

#: templates/account/messages/login_code_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been mailed to %(email)s."
msgid "A sign-in code has been sent to %(recipient)s."
msgstr "Prisijungimo kodas buvo nusiųstas į %(email)s."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Slaptažodis sėkmingai pakeistas."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Slaptažodis pakeistas sėkmingai."

#: templates/account/messages/phone_verification_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been mailed to %(email)s."
msgid "A verification code has been sent to %(phone)s."
msgstr "Prisijungimo kodas buvo nusiųstas į %(email)s."

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr ""

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Pirminis el. pašto adresas pakeistas sėkmingai."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "Keisti slaptažodį"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "Pamiršote slaptažodį?"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Pamiršote slaptažodį? Įveskite savo el. pašto adresą žemiau ir mes išsiūsime "
"jums laišką, kurio pagalba galėsite pasikeisti slaptažodį."

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "Atstatyti mano slaptažodį"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr "Prašome susisiekti su mumis jei negalite atstatyti slaptažodžio."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Išsiuntėme jums patvirtinimo laišką. Prašome sekite nuorodą pateiktą laiške. "
"Susisiekite su mumis jei negausite laiško per kelias minutes."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Klaidinga atpažinimo žymė"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Slaptažodžio atstatymo nuoroda klaidinga, taip gali būti dėl to, kad nuoroda "
"jau buvo kartą panaudota.  Prašome <a "
"href=\"%(passwd_reset_url)s\">pakartoti slaptažodžio atstatymą</a>."

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "Jūsų slaptažodis pakeistas."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "Nustatyti slaptažodį"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
#, fuzzy
#| msgid "Change to"
msgid "Change Phone"
msgstr "Pakeista į"

#: templates/account/phone_change.html:18
#, fuzzy
#| msgid "Current"
msgid "Current phone"
msgstr "Esamas"

#: templates/account/phone_change.html:22
#, fuzzy
#| msgid "Your email address is still pending verification."
msgid "Your phone number is still pending verification."
msgstr "Jūsų el. pašto adresas dar vis laukia patvirtinimo."

#: templates/account/reauthenticate.html:6
msgid "Enter your password:"
msgstr "Įrašykite slaptažodį:"

#: templates/account/request_login_code.html:12
#, fuzzy
#| msgid ""
#| "You will receive an email containing a special code for a password-free "
#| "sign-in."
msgid "You will receive a special code for a password-free sign-in."
msgstr "Gausite laišką su specialiu kodu prisijungimui be slaptažodžio."

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr "Prašyti Kodo"

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr "Kiti prisijungimo pasirinkimai"

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Registracija"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "Registruotis"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr "Jau turite paskyrą? Prašome %(link)sprisijungti%(end_link)s."

#: templates/account/signup.html:39
#, fuzzy
#| msgid "Sign in with a passkey"
msgid "Sign up using a passkey"
msgstr "Prisijunkite su prieigos raktu"

#: templates/account/signup_by_passkey.html:8
#, fuzzy
#| msgid "Sign Up"
msgid "Passkey Sign Up"
msgstr "Registruotis"

#: templates/account/signup_by_passkey.html:36
#, fuzzy
#| msgid "Other sign-in options"
msgid "Other options"
msgstr "Kiti prisijungimo pasirinkimai"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Registracija uždaryta"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "Atsiprašome, tačiau registracija šiuo metu yra uždaryta."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Pastaba"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "Jūs jau esate prisijungęs, kaip %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Įspėjimas:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Šiuo metu jūs neturite nustatyto el. pašto adreso. Rekomenduojame pridėti "
"el. pašto adresą, kad gautumėte pranešimus, galėtumėte atstatyti slaptažodį "
"ir pan."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Patvirtinkite savo el. pašto adresą"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Išsiuntėme jums laišką patvirtinimui. Sekite laiške pateiktą nuorodą, kad "
"užbaigtumėte registraciją. Jei nematote laiško pagrindiniame skyriuje, "
"patikrinkite šlamšto skyrių. Prašome susisiekti su mumis, jei laiško "
"negavote per kelias minutes."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Šioje svetainės vietoje privalome gauti iš jūsų patvirtinimą,\n"
"kad jūs tikrai esate tas asmuo, kaip teigiate. Dėl šios priežasties\n"
"prašome patvirtinti el. pašto adreso nuosavybę. "

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Išsiuntėme jums patvirtinimo laišką.\n"
"Prašome sekite nuorodą pateiktą laiške. Jei laiško nematote, patikrinkite "
"šlamšto skyrių. Susisiekite su mumis\n"
"jei negausite laiško per kelias minutes."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Pastaba:</strong> vis dar galite <a href=\"%(email_url)s\">pakeisti "
"savo el. pašto adresą</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "Žinutės:"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "Meniu:"

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Paskyros ryšiai"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "Dviejų Faktorių Autentifikacija"

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr "Sesijos"

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr ""

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr ""

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr ""

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr "Autentifikacijos Aplikacija Aktyvuota"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr ""

#: templates/mfa/email/totp_deactivated_subject.txt:3
msgid "Authenticator App Deactivated"
msgstr "Autentifikacijos Aplikacija Deaktyvuota"

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr ""

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr ""

#: templates/mfa/email/webauthn_removed_message.txt:4
msgid "A security key has been removed."
msgstr "Saugumo raktas buvo pašalintas."

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr ""

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr ""

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr ""

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr ""

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr ""

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr ""

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr ""

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr ""

#: templates/mfa/index.html:62
msgid "Manage"
msgstr ""

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr ""

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr ""

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr ""

#: templates/mfa/index.html:96
msgid "View"
msgstr ""

#: templates/mfa/index.html:102
msgid "Download"
msgstr ""

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr ""

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr ""

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr ""

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr ""

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr "Įveskite autentifikatoriaus kodą:"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr ""

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr ""

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr ""

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr ""

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr ""

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "Autentifikatoriaus paslaptis"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr ""

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr ""

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr ""

#: templates/mfa/webauthn/add_form.html:7
msgid "Add Security Key"
msgstr "Pridėti Saugumo Raktą"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
msgid "Remove Security Key"
msgstr "Pašalinti Saugumo Raktą"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
msgid "Are you sure you want to remove this security key?"
msgstr "Ar tikrai norite pašalinti šį saugumo raktą?"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:37
msgid "Security key"
msgstr "Saugumo raktas"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:41
msgid "Unspecified"
msgstr "Nenurodytas"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr ""

#: templates/mfa/webauthn/edit_form.html:7
msgid "Edit Security Key"
msgstr "Keisti Saugumo Raktą"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr ""

#: templates/mfa/webauthn/signup_form.html:7
#, fuzzy
#| msgid "Current Password"
msgid "Create Passkey"
msgstr "Esamas slaptažodis"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""

#: templates/mfa/webauthn/signup_form.html:21
#, fuzzy
#| msgid "created"
msgid "Create"
msgstr "sukurtas"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr ""

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Third-Party Login Failure"
msgstr "Trečiosios Šalies Prisijungimo Klaida"

#: templates/socialaccount/authentication_error.html:12
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr "Įvyko nenumatyta klaida bandant prisijungti trčiosios šalies paskyra."

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr ""
"Galite prisijungti prie savo paskyros naudodami vieną iš galimų trečios "
"šalies paskyrų:"

#: templates/socialaccount/connections.html:46
msgid "You currently have no third-party accounts connected to this account."
msgstr ""
"Šiuo metu jūs neturite nei vienos prijungtos trečiosios šalies paskyros."

#: templates/socialaccount/connections.html:50
msgid "Add a Third-Party Account"
msgstr "Pridėti Trečiosios Šalies paskyrą"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr ""
"Trečiosios šalies paskyra iš %(provider)s buvo prijungta prie jūsų paskyros."

#: templates/socialaccount/email/account_connected_subject.txt:3
msgid "Third-Party Account Connected"
msgstr "Trečiosios Šalies Paskyra Prijungta"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr ""
"Trečiosios šalies paskyra iš %(provider)s buvo atjungta nuo jūsų paskyros."

#: templates/socialaccount/email/account_disconnected_subject.txt:3
msgid "Third-Party Account Disconnected"
msgstr "Trečiosios Šalies Paskyra Atjungta"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "Prijungti %(provider)s"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr ""
"Jūs tuojaus prijungsite naują trečiosios šalies paskyrą iš %(provider)s."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "Prisijungkite Su %(provider)s"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr ""
"Jūs tuojaus prisijungsite naudojantis trečiosios šalies paskyra iš "
"%(provider)s."

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr "Tęsti"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Prisijungimas atšauktas"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Nusprendėte atšaukti prisijungimą naudojant vieną iš esamų paskyrų.  Jei tai "
"buvo klaida, prašome pakartoti <a href=\"%(login_url)s\">prisijungimą</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The third-party account has been connected."
msgstr "Trečiosios šalies paskyra buvo prijungta."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The third-party account has been disconnected."
msgstr "Trečiosios šalies paskyra buvo atjungta."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Jūs beveik prisijungėte prie %(site_name)s naudodami %(provider_name)s\n"
"paskyrą. Liko paskutinis žingsnis, užpildyti sekančią formą:"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr "Arba naudokite trečiąją šalį"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr "Atsijungta nuo visų kitų sesijų."

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr "Pradėta"

#: templates/usersessions/usersession_list.html:24
msgid "IP Address"
msgstr "IP Adresas"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr "Naršyklė"

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr "Paskutinį kartą matyta"

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "Esamas"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr "Atjungti Kitas Sesijas"

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr "Vartotojo Sesijos"

#: usersessions/models.py:92
msgid "session key"
msgstr "sesijos raktas"

#, fuzzy
#~| msgid "Account Connections"
#~ msgid "Account Connection"
#~ msgstr "Paskyros ryšiai"

#, python-brace-format
#~ msgid "Password must be a minimum of {0} characters."
#~ msgstr "Slaptažodis turi būti sudarytas mažiausiai iš {0} simbolių."

#, fuzzy, python-format
#~| msgid ""
#~| "Hello from %(site_name)s!\n"
#~| "\n"
#~| "You're receiving this e-mail because you or someone else has requested a "
#~| "password for your user account at %(site_domain)s.\n"
#~| "It can be safely ignored if you did not request a password reset. Click "
#~| "the link below to reset your password."
#~ msgid ""
#~ "You are receiving this email because you or someone else has requested a\n"
#~ "password for your user account. However, we do not have any record of a "
#~ "user\n"
#~ "with email %(email)s in our database.\n"
#~ "\n"
#~ "This mail can be safely ignored if you did not request a password reset.\n"
#~ "\n"
#~ "If it was you, you can sign up for an account using the link below."
#~ msgstr ""
#~ "Su jumis sveikinasi %(site_name)s\n"
#~ "\n"
#~ "Jūs gavote šį laišką, kadangi jūs arba kažkas kitas pateikė slaptažodžio "
#~ "keitimo užklausą paskyrai susietai su šiuo el. pašto adresu iš "
#~ "%(site_domain)s.\n"
#~ "Jei jūs neteikėte slaptažodžio keitimo užklausos, galite ignoruoti šį "
#~ "laišką. Kad pakeistumėte savo slaptažodį sekite žemiau esančią nuorodą."

#, fuzzy
#~| msgid "The following email addresses are associated with your account:"
#~ msgid "The following email address is associated with your account:"
#~ msgstr "Šie el. pašto adresas yra susieti su jūsų paskyra:"

#, fuzzy
#~| msgid "Confirm Email Address"
#~ msgid "Change Email Address"
#~ msgstr "Patvirtinkite el. pašto adresą"

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "Prašome prisijungti viena\n"
#~ "iš jūsų turimų trečios šalies paskyrų. Arba, <a "
#~ "href=\"%(signup_url)s\">susikurkite</a>\n"
#~ "naują %(site_name)s paskyrą ir prisijunkite žemiau:"

#~ msgid "or"
#~ msgstr "arba"

#~ msgid "change password"
#~ msgstr "keisti slaptažodį"

#~ msgid "OpenID Sign In"
#~ msgstr "OpenID prisijungimas"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Šis el. pašto adresas jau susietas su kita paskyra."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Išsiuntėme jums laišką. Prašome susisiekti su mums jei per kelias minutes "
#~ "negausite laiško."

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "Pateiktas prisijungimo vardas ir/arba slaptažodis yra neteisingi."

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr "Naudotojo vardui galima naudoti tik raides, skaičius ir @/./+/-/_."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "Šis naudotojo vardas jau užimtas. Prašome pasirinkti kitą."

#, fuzzy
#~| msgid "Sign In"
#~ msgid "Shopify Sign In"
#~ msgstr "Prisijungti"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "Jūs patvirtinote, kad <a href=\"mailto:%(email)s\">%(email)s</a> yra "
#~ "%(user_display)s naudotojo el. pašto adresas."
