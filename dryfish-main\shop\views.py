from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.forms import UserCreationForm
from django.http import HttpResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.conf import settings
from .models import Order, ProductReview
from .forms import OrderForm, ProductReviewForm
from .payment_utils import PaymentProcessor, OfflinePaymentHandler
import csv
import json

def home(request):
    # Get product choices from the model to ensure consistency
    from .models import Order
    products = [choice[0] for choice in Order.PRODUCT_CHOICES]

    # Get 5-star reviews grouped by product (limit 3 per product)
    five_star_reviews = {}
    for product_choice in Order.PRODUCT_CHOICES:
        product_name = product_choice[0]
        reviews = ProductReview.objects.filter(
            product=product_name,
            rating=5
        ).select_related('user').order_by('-created_at')[:3]

        if reviews:
            five_star_reviews[product_name] = {
                'display_name': product_choice[1],
                'reviews': reviews
            }

    context = {
        'products': products,
        'five_star_reviews': five_star_reviews,
    }
    return render(request, 'shop/home.html', context)

def products(request):
    """Products page with detailed information"""
    from .models import Order

    # Product information with prices and reviews
    products_info = [
        {
            'name': 'motha kendai',
            'display_name': 'Motha Kendai',
            'tamil_name': 'மொத்த கெண்டை',
            'pricing': Order.PRODUCT_PRICING.get('motha kendai', {}),
            'icon': '🐟',
            'description': 'Firm, thick-fleshed dry fish known for its intense flavor and chewy texture.',
            'average_rating': ProductReview.get_average_rating('motha kendai'),
            'review_count': ProductReview.get_review_count('motha kendai'),
            'star_display': ProductReview.get_star_display_for_product('motha kendai'),
        },
        {
            'name': 'netthili',
            'display_name': 'Netthili',
            'tamil_name': 'நெத்திலி Dry Fish / Anchovy',
            'pricing': Order.PRODUCT_PRICING.get('netthili', {}),
            'icon': '🐟',
            'description': 'Small, slender dry fish widely loved in South India with salty-sweet flavor.',
            'average_rating': ProductReview.get_average_rating('netthili'),
            'review_count': ProductReview.get_review_count('netthili'),
            'star_display': ProductReview.get_star_display_for_product('netthili'),
        },
        {
            'name': 'vaalai',
            'display_name': 'Vaalai',
            'tamil_name': 'வாளை Dry Fish',
            'pricing': Order.PRODUCT_PRICING.get('vaalai', {}),
            'icon': '🐟',
            'description': 'Medium-sized dry fish with soft flesh and mild aroma, perfect for gravies.',
            'average_rating': ProductReview.get_average_rating('vaalai'),
            'review_count': ProductReview.get_review_count('vaalai'),
            'star_display': ProductReview.get_star_display_for_product('vaalai'),
        },
        {
            'name': 'goa netthili',
            'display_name': 'Goa Netthili',
            'tamil_name': 'Goan Anchovy Dry Fish',
            'pricing': Order.PRODUCT_PRICING.get('goa netthili', {}),
            'icon': '🐟',
            'description': 'Coastal variation of netthili, larger and saltier with crispy texture.',
            'average_rating': ProductReview.get_average_rating('goa netthili'),
            'review_count': ProductReview.get_review_count('goa netthili'),
            'star_display': ProductReview.get_star_display_for_product('goa netthili'),
        },
        {
            'name': 'yeera',
            'display_name': 'Yeera',
            'tamil_name': 'Dry Shrimp / உலர்ந்த இறால்',
            'pricing': Order.PRODUCT_PRICING.get('yeera', {}),
            'icon': '🦐',
            'description': 'Dried shrimp, one of the most flavor-rich seafood ingredients.',
            'average_rating': ProductReview.get_average_rating('yeera'),
            'review_count': ProductReview.get_review_count('yeera'),
            'star_display': ProductReview.get_star_display_for_product('yeera'),
        },
    ]

    return render(request, 'shop/products.html', {'products_info': products_info})


def product_detail(request, product_name):
    """Product detail page with pricing information"""
    # Validate product name
    valid_products = [choice[0] for choice in Order.PRODUCT_CHOICES]
    if product_name not in valid_products:
        messages.error(request, 'Product not found.')
        return redirect('products')

    # Get product information
    product_display_name = dict(Order.PRODUCT_CHOICES)[product_name]
    pricing = Order.PRODUCT_PRICING.get(product_name, {})

    # Calculate sample prices for common quantities
    sample_prices = {}
    common_quantities = [100, 500, 1000, 2000, 5000]  # in grams

    for qty in common_quantities:
        price = Order.calculate_price_for_quantity(product_name, qty)
        original_price = Order.get_original_price(product_name, qty)
        discount = Order.calculate_discount(product_name, qty)

        # Format quantity display
        if qty >= 1000:
            kg = qty / 1000
            if kg == int(kg):
                qty_display = f"{int(kg)}kg"
            else:
                qty_display = f"{kg:.1f}kg"
        else:
            qty_display = f"{qty}g"

        sample_prices[qty] = {
            'quantity_display': qty_display,
            'price': round(price, 2),
            'original_price': round(original_price, 2),
            'discount': round(discount, 2),
            'savings_percent': round((discount / original_price * 100), 1) if original_price > 0 else 0
        }

    # Get reviews for this product
    reviews = ProductReview.objects.filter(product=product_name).select_related('user').order_by('-created_at')[:5]
    average_rating = ProductReview.get_average_rating(product_name)
    review_count = ProductReview.get_review_count(product_name)
    star_display = ProductReview.get_star_display_for_product(product_name)

    context = {
        'product_name': product_name,
        'product_display_name': product_display_name,
        'pricing': pricing,
        'sample_prices': sample_prices,
        'reviews': reviews,
        'average_rating': average_rating,
        'review_count': review_count,
        'star_display': star_display,
    }

    return render(request, 'shop/product_detail.html', context)

def order(request):
    """Order page with form - supports pre-filled product and quantity"""
    # Get pre-filled product and quantity from URL parameters
    prefilled_product = request.GET.get('product')
    prefilled_quantity = request.GET.get('quantity')

    # Validate prefilled data
    hide_product_fields = False
    product_display_name = None
    quantity_display = None

    if prefilled_product and prefilled_quantity:
        # Validate product
        valid_products = [choice[0] for choice in Order.PRODUCT_CHOICES]
        if prefilled_product in valid_products:
            try:
                quantity_grams = int(prefilled_quantity)
                if 100 <= quantity_grams <= 10000:
                    hide_product_fields = True
                    product_display_name = dict(Order.PRODUCT_CHOICES)[prefilled_product]

                    # Format quantity display
                    if quantity_grams >= 1000:
                        kg = quantity_grams / 1000
                        if kg == int(kg):
                            quantity_display = f"{int(kg)}kg"
                        else:
                            quantity_display = f"{kg:.1f}kg"
                    else:
                        quantity_display = f"{quantity_grams}g"
            except (ValueError, TypeError):
                pass

    if request.method == 'POST':
        form = OrderForm(
            request.POST,
            hide_product_fields=hide_product_fields,
            prefilled_product=prefilled_product,
            prefilled_quantity=prefilled_quantity
        )
        if form.is_valid():
            try:
                # Create order from form data
                new_order = form.save(commit=False)
                new_order.user = request.user if request.user.is_authenticated else None
                new_order.payment_status = 'pending'
                new_order.save()

                messages.success(request, f'Order #{new_order.id} created successfully! Please complete payment.')
                # Redirect to payment page
                return redirect('payment', order_id=new_order.id)

            except Exception as e:
                messages.error(request, f'Error creating order: {str(e)}. Please try again.')
        else:
            # Form has validation errors
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field.replace("_", " ").title()}: {error}')
    else:
        # Initialize form with user data and pre-filled product/quantity
        initial_data = {}
        if request.user.is_authenticated:
            initial_data['name'] = request.user.get_full_name() or request.user.first_name
            initial_data['email'] = request.user.email

        form = OrderForm(
            initial=initial_data,
            hide_product_fields=hide_product_fields,
            prefilled_product=prefilled_product,
            prefilled_quantity=prefilled_quantity
        )

    context = {
        'form': form,
        'hide_product_fields': hide_product_fields,
        'product_display_name': product_display_name,
        'quantity_display': quantity_display,
        'prefilled_product': prefilled_product,
        'prefilled_quantity': prefilled_quantity,
    }

    return render(request, 'shop/order.html', context)

@login_required
def payment(request, order_id):
    """Payment selection and processing page"""
    order = get_object_or_404(Order, id=order_id, user=request.user)

    if request.method == 'POST':
        payment_method = request.POST.get('payment_method')
        order.payment_method = payment_method

        if payment_method in ['cash_on_delivery', 'bank_transfer']:
            # Handle offline payments
            OfflinePaymentHandler.process_offline_payment(order, payment_method)
            return redirect('payment_confirmation', order_id=order.id)
        else:
            # Handle online payments
            order.transaction_id = PaymentProcessor.generate_transaction_id()
            order.save()
            return redirect('payment_process', order_id=order.id)

    return render(request, 'shop/payment.html', {'order': order})

@login_required
def payment_process(request, order_id):
    """Process online payments"""
    order = get_object_or_404(Order, id=order_id, user=request.user)

    if order.payment_method == 'google_pay':
        # Google Pay app redirect
        google_pay_url = PaymentProcessor.get_google_pay_url(order)
        context = {
            'order': order,
            'app_url': google_pay_url,
            'app_name': 'Google Pay',
            'app_icon': 'fab fa-google-pay',
            'fallback_url': PaymentProcessor.get_upi_payment_url(order),
            'payment_method': 'google_pay'
        }
        return render(request, 'shop/app_payment_redirect.html', context)

    elif order.payment_method == 'phonepe':
        # PhonePe app redirect
        phonepe_url = PaymentProcessor.get_phonepe_url(order)
        context = {
            'order': order,
            'app_url': phonepe_url,
            'app_name': 'PhonePe',
            'app_icon': 'fas fa-mobile-alt',
            'fallback_url': PaymentProcessor.get_upi_payment_url(order),
            'payment_method': 'phonepe'
        }
        return render(request, 'shop/app_payment_redirect.html', context)

    elif order.payment_method == 'paytm':
        # Paytm app redirect
        paytm_url = PaymentProcessor.get_paytm_url(order)
        context = {
            'order': order,
            'app_url': paytm_url,
            'app_name': 'Paytm',
            'app_icon': 'fas fa-wallet',
            'fallback_url': PaymentProcessor.get_upi_payment_url(order),
            'payment_method': 'paytm'
        }
        return render(request, 'shop/app_payment_redirect.html', context)

    elif order.payment_method == 'upi':
        # UPI payment
        upi_url = PaymentProcessor.get_upi_payment_url(order)
        context = {
            'order': order,
            'upi_url': upi_url
        }
        return render(request, 'shop/upi_payment.html', context)

    else:
        # Fallback for other payment methods
        messages.error(request, 'Payment method not supported')
        return redirect('payment', order_id=order.id)

@login_required
def payment_confirmation(request, order_id):
    """Payment confirmation page"""
    order = get_object_or_404(Order, id=order_id, user=request.user)

    context = {
        'order': order,
        'bank_details': OfflinePaymentHandler.get_bank_details() if order.payment_method == 'bank_transfer' else None,
        'cod_info': OfflinePaymentHandler.get_cod_instructions() if order.payment_method == 'cash_on_delivery' else None
    }

    return render(request, 'shop/payment_confirmation.html', context)

@csrf_exempt
@login_required
def payment_verify(request):
    """Verify Razorpay payment"""
    if request.method == 'POST':
        try:
            payment_id = request.POST.get('razorpay_payment_id')
            order_id = request.POST.get('razorpay_order_id')
            signature = request.POST.get('razorpay_signature')

            # Verify payment signature
            if PaymentProcessor.verify_razorpay_payment(payment_id, order_id, signature):
                # Find order by transaction_id (which is the razorpay_order_id)
                order = Order.objects.get(transaction_id=order_id, user=request.user)

                # Mark payment as completed
                order.payment_status = 'completed'
                order.paid = True
                order.save()

                messages.success(request, f'Payment successful! Order #{order.id} has been confirmed and is being processed.')
                return JsonResponse({'status': 'success', 'redirect_url': '/orders/'})
            else:
                return JsonResponse({'status': 'error', 'message': 'Payment verification failed'})

        except Order.DoesNotExist:
            return JsonResponse({'status': 'error', 'message': 'Order not found'})
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)})

    return JsonResponse({'status': 'error', 'message': 'Invalid request'})

@login_required
def payment_success(request, order_id):
    """Payment success callback - redirect to view orders"""
    order = get_object_or_404(Order, id=order_id, user=request.user)

    # Mark payment as completed
    order.payment_status = 'completed'
    order.paid = True
    order.save()

    messages.success(request, f'Payment successful! Order #{order.id} has been confirmed and is being processed.')
    return redirect('view_orders')

@login_required
def view_orders(request):
    """View user's orders with beautiful UI"""
    orders = Order.objects.filter(user=request.user).order_by('-created_at')
    return render(request, 'shop/view_orders.html', {'orders': orders})

@login_required
def export_orders(request):
    if not request.user.is_authenticated:
        return redirect('home')

    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="orders.csv"'
    writer = csv.writer(response)
    writer.writerow([
        'Name', 'Mobile', 'Street Address', 'Area/Locality', 'City', 'State', 'PIN Code',
        'Additional Address', 'Product', 'Quantity', 'Payment Method', 'Payment Status',
        'Total Amount', 'Paid'
    ])
    for order in Order.objects.filter(user=request.user):
        writer.writerow([
            order.name, order.mobile, order.street_name or '', order.place_name or '',
            order.city or '', order.state or '', order.pin_code or '', order.address or '',
            order.product, order.quantity, order.get_payment_method_display(),
            order.get_payment_status_display(), order.total_amount, order.paid
        ])
    return response

def login_view(request):
    """Manual login view"""
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        user = authenticate(request, username=username, password=password)
        if user is not None:
            login(request, user)
            messages.success(request, 'Successfully logged in!')
            return redirect('home')
        else:
            messages.error(request, 'Invalid username or password.')

    return render(request, 'registration/login.html')

def signup_view(request):
    """Manual signup view with automatic login"""
    if request.method == 'POST':
        form = UserCreationForm(request.POST)
        if form.is_valid():
            # Create the user
            user = form.save()
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password1')

            # Authenticate and login the user automatically
            authenticated_user = authenticate(request, username=username, password=password)
            if authenticated_user is not None:
                login(request, authenticated_user)
                messages.success(request, f'Welcome {username}! Your account has been created and you are now logged in.')
                # Check if there's a next parameter
                next_url = request.GET.get('next', 'home')
                return redirect(next_url)
            else:
                messages.error(request, f'Account created for {username} but automatic login failed. Please log in manually.')
                return redirect('account_login')
        else:
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')
    else:
        form = UserCreationForm()

    return render(request, 'registration/signup.html', {'form': form})


@login_required
def submit_review(request, product_name):
    """Submit a review for a product"""
    # Validate product name
    valid_products = [choice[0] for choice in ProductReview.PRODUCT_CHOICES]
    if product_name not in valid_products:
        messages.error(request, 'Invalid product selected.')
        return redirect('products')

    # Check if user has already reviewed this product
    existing_review = ProductReview.objects.filter(user=request.user, product=product_name).first()

    if request.method == 'POST':
        form = ProductReviewForm(request.POST, user=request.user, instance=existing_review)
        if form.is_valid():
            form.save()
            if existing_review:
                messages.success(request, f'Your review for {dict(ProductReview.PRODUCT_CHOICES)[product_name]} has been updated!')
            else:
                messages.success(request, f'Thank you for reviewing {dict(ProductReview.PRODUCT_CHOICES)[product_name]}!')
            return redirect('products')
        else:
            # Handle form errors
            for field, errors in form.errors.items():
                field_name = form.fields[field].label if field in form.fields else field
                for error in errors:
                    messages.error(request, f"{field_name}: {error}")

            # Handle non-field errors
            for error in form.non_field_errors():
                messages.error(request, error)
    else:
        initial_data = {'product': product_name}
        form = ProductReviewForm(user=request.user, initial=initial_data, instance=existing_review)

    product_display_name = dict(ProductReview.PRODUCT_CHOICES)[product_name]
    context = {
        'form': form,
        'product_name': product_name,
        'product_display_name': product_display_name,
        'existing_review': existing_review,
    }

    return render(request, 'shop/submit_review.html', context)


@login_required
def product_reviews(request, product_name):
    """Display all reviews for a product"""
    # Validate product name
    valid_products = [choice[0] for choice in ProductReview.PRODUCT_CHOICES]
    if product_name not in valid_products:
        messages.error(request, 'Invalid product selected.')
        return redirect('products')

    reviews = ProductReview.objects.filter(product=product_name).select_related('user')
    average_rating = ProductReview.get_average_rating(product_name)
    review_count = ProductReview.get_review_count(product_name)
    star_display = ProductReview.get_star_display_for_product(product_name)

    # Check if current user can review this product
    can_review = False
    user_has_reviewed = False

    if request.user.is_authenticated:
        # Check if user has completed orders for this product
        user_orders = Order.objects.filter(
            user=request.user,
            product=product_name,
            payment_status='completed'
        )
        can_review = user_orders.exists()

        # Check if user has already reviewed
        user_has_reviewed = ProductReview.objects.filter(
            user=request.user,
            product=product_name
        ).exists()

    product_display_name = dict(ProductReview.PRODUCT_CHOICES)[product_name]

    context = {
        'product_name': product_name,
        'product_display_name': product_display_name,
        'reviews': reviews,
        'average_rating': average_rating,
        'review_count': review_count,
        'star_display': star_display,
        'can_review': can_review,
        'user_has_reviewed': user_has_reviewed,
    }

    return render(request, 'shop/product_reviews.html', context)


@csrf_exempt
def get_product_price(request):
    """AJAX endpoint to get product price based on quantity"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            product = data.get('product')
            quantity_grams = data.get('quantity_grams')

            if product and quantity_grams:
                try:
                    quantity_grams = int(quantity_grams)
                except (ValueError, TypeError):
                    return JsonResponse({'success': False, 'error': 'Invalid quantity'})

                price = Order.calculate_price_for_quantity(product, quantity_grams)
                original_price = Order.get_original_price(product, quantity_grams)
                discount = Order.calculate_discount(product, quantity_grams)

                # Format quantity display
                if quantity_grams >= 1000:
                    kg = quantity_grams / 1000
                    if kg == int(kg):
                        quantity_display = f"{int(kg)}kg"
                    else:
                        quantity_display = f"{kg:.1f}kg"
                else:
                    quantity_display = f"{quantity_grams}g"

                return JsonResponse({
                    'success': True,
                    'price': round(price, 2),
                    'original_price': round(original_price, 2),
                    'discount': round(discount, 2),
                    'savings': round(discount, 2),
                    'quantity_display': quantity_display
                })

            return JsonResponse({'success': False, 'error': 'Invalid parameters'})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})

def logout_view(request):
    """Custom logout view with confirmation"""
    if request.method == 'POST':
        logout(request)
        messages.success(request, 'You have been successfully logged out!')
        return redirect('home')

    return render(request, 'registration/logout.html')
